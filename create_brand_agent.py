import json
import os
from typing import List, Dict, Optional
from app.openai_client import OpenAIClient
from prompts.prompt_manager import prompt_manager
from app.web_scraper import get_screenshot_as_base64

# Placeholder for the comprehensive JSON schema
COMPREHENSIVE_BRAND_SCHEMA = {
  "metadata": {
    "brand_id": "",
    "brand_name": "",
    "tagline": None,
    "description": None,
    "mission_statement": None,
    "vision_statement": None,
    "values": [],
    "target_audience": None,
    "brand_story": None,
    "tone_of_voice": {
      "keywords": [],
      "description": None
    },
    "locales_supported": []
  },
  "logo": {
    "primary": {
      "url": None,
      "placement": None,
      "max_height_px": None,
      "min_clearspace_ratio": None,
      "description": None,
      "usage_guidelines": None
    },
    "variations": []
  },
  "color_system": {
    "primary": [],
    "secondary": [],
    "accent": [],
    "neutral": []
  },
  "typography": {
    "primary_font": {
      "family": None,
      "usage": None
    },
    "secondary_font": {
      "family": None,
      "usage": None
    },
    "other_fonts": []
  },
  "imagery_style": {
    "description": None,
    "examples": []
  },
  "brand_assets_urls": []
}

def generate_brand_json_from_data(
    brand_name: str,
    text_content: List[str],
    screenshots_base64: List[str]
) -> Dict:
    print(f"[DEBUG] Starting brand JSON generation for: {brand_name}")
    print(f"[DEBUG] Text content items: {len(text_content)}")
    print(f"[DEBUG] Screenshots provided: {len(screenshots_base64)}")

    if text_content:
        total_text_length = sum(len(text) for text in text_content)
        print(f"[DEBUG] Total text content length: {total_text_length} characters")
        print(f"[DEBUG] First text content preview: {text_content[0][:200]}..." if text_content[0] else "[DEBUG] First text content is empty")

    openai_client = OpenAIClient()
    print(f"[DEBUG] OpenAI client initialized")

    # Use centralized prompt manager for brand analysis
    vision_prompt = prompt_manager.get_brand_analysis_prompt(brand_name, text_content, COMPREHENSIVE_BRAND_SCHEMA)

    print(f"[DEBUG] Vision prompt created, length: {len(vision_prompt)} characters")

    # Use all screenshots (up to 8) for the Vision API call
    if screenshots_base64:
        # Limit to maximum 8 screenshots for the API
        screenshots_to_send = screenshots_base64[:8]
        print(f"[DEBUG] Using {len(screenshots_to_send)} screenshots for analysis (max 8)")

        for i, screenshot in enumerate(screenshots_to_send):
            print(f"[DEBUG] Screenshot {i+1} length: {len(screenshot)} characters")

        print("Sending data to OpenAI Vision API...")
        try:
            generated_json = openai_client.analyze_images_and_generate_json(screenshots_to_send, vision_prompt)
            print(f"[DEBUG] Vision API call completed successfully")
            print(f"[DEBUG] Generated JSON keys: {list(generated_json.keys()) if isinstance(generated_json, dict) else 'Not a dict'}")
            return generated_json
        except Exception as e:
            print(f"[ERROR] Error calling Vision API: {type(e).__name__}: {e}")
            print(f"[DEBUG] Full exception details: {str(e)}")
            import traceback
            print(f"[DEBUG] Traceback: {traceback.format_exc()}")
            return {"status": "error", "message": f"Error calling Vision API: {e}"}
    else:
        print(f"[ERROR] No screenshots provided for analysis")
        return {"status": "error", "message": "No screenshots provided for analysis."}


# This part is for demonstration/testing if run directly, not for agent execution
if __name__ == '__main__':
    # This section would typically be handled by the agent's orchestration
    # For a direct run, you'd need to manually provide data
    print("This script is designed to be called by the agent with collected data.")
    print("Example usage (manual data):")
    sample_text = ["Coca-Cola is a global brand."]
    # You would replace this with an actual base64 encoded image
    sample_screenshot = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII=" # A 1x1 transparent PNG
    
    # brand_data = generate_brand_json_from_data("Coca-Cola", sample_text, [sample_screenshot])
    # print(json.dumps(brand_data, indent=2))