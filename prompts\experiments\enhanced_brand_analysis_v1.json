{"name": "enhanced_brand_analysis_v1", "description": "Enhanced brand analysis with improved color extraction and typography detection", "prompt_type": "brand_creation", "created_date": "2025-07-02", "version": "v1.1", "changes": ["Added more specific color extraction guidelines", "Enhanced typography detection instructions", "Improved logo variation identification", "Added brand asset URL extraction focus", "Enhanced visual pattern recognition instructions"], "prompt_content": "Given the following text content and visual information (multiple screenshots) related to the brand '{brand_name}', extract comprehensive brand details with enhanced focus on visual elements. Analyze ALL provided screenshots along with the text content to populate the JSON schema with maximum detail.\n\nENHANCED ANALYSIS FOCUS:\n- Brand mission, vision, values, target audience, and brand story\n- Tone of voice keywords and descriptions with emotional context\n- Logo details: primary logo URL, descriptions, usage guidelines, and ALL variations\n- Color system: Extract EXACT hex codes when visible, identify color relationships and usage patterns\n- Typography: Identify font families, weights, sizes, and specific usage contexts\n- Imagery style: Detailed descriptions, visual themes, and photographic styles\n- Brand asset URLs: Official websites, social media, press resources, style guides\n\nCOLOR EXTRACTION GUIDELINES:\n- Look for color swatches, style guides, or brand documentation in screenshots\n- Extract exact hex codes when visible (#RRGGBB format)\n- Identify primary, secondary, accent, and neutral color relationships\n- Note color usage patterns (headers, backgrounds, accents, text)\n- Look for color names or descriptions in brand documentation\n\nTYPOGRAPHY DETECTION:\n- Identify specific font families used in headers, body text, and UI elements\n- Note font weights (light, regular, bold, etc.) and their usage\n- Look for typography guidelines or font specifications\n- Identify custom or branded fonts\n- Note text hierarchy and sizing patterns\n\nLOGO AND VISUAL ASSETS:\n- Identify all logo variations (horizontal, vertical, icon-only, etc.)\n- Look for logo usage guidelines and spacing requirements\n- Extract logo file URLs when visible\n- Note logo placement patterns and contexts\n- Identify brand symbols, icons, or visual elements\n\nEXTRACTION GUIDELINES:\n- Cross-reference information between text content and visual elements\n- Prioritize information found in official brand guidelines or style guides\n- If information is not explicitly found, set fields to null or empty arrays\n- Do NOT make up information - only extract what is clearly visible or stated\n- Pay special attention to brand documentation, style guides, and official materials\n- Look for downloadable assets, press kits, or media resources\n\nExtract maximum detail from both text and visual content. Focus on actionable brand information that can be used for consistent brand application.\n\nJSON Schema to populate:\n{schema}\n\nTextual Content:\n{text_content}\n\nVisual Analysis (analyze all provided screenshots for comprehensive brand insights):", "performance_notes": [], "test_results": []}