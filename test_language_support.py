#!/usr/bin/env python3
"""
Test script to verify language support in image generation prompts.
"""

import sys
import os
sys.path.insert(0, os.path.abspath('.'))

from prompts.image_generation import build_image_generation_prompt
from prompts.goal_processing import build_goal_processing_prompt
from app.goal_processor import ImageRequirement

def test_dutch_language_support():
    """Test that Dutch language instructions are included for ledlichtdiscounter."""
    
    # Mock brand profile with Dutch locales
    brand_profile = {
        'metadata': {
            'brand_name': 'Ledlichtdiscounter',
            'description': 'LED lighting specialist',
            'tone_keywords': ['professioneel', 'betrouwbaar'],
            'mission_statement': 'Providing quality LED lighting',
            'locales_supported': ['Nederland', 'België']
        },
        'color_system': {
            'primary': [{'name': 'Blue', 'hex': '#0066CC'}],
            'secondary': [],
            'accent': [],
            'neutral': []
        },
        'typography': {
            'primary_font': {'family': 'Arial'},
            'secondary_font': {'family': 'Helvetica'}
        }
    }
    
    # Mock image requirement
    image_req = ImageRequirement(
        title="Product Overview",
        purpose="Showcase LED products",
        content_focus="LED lamps",
        key_points=["Energy efficient", "Long lasting"],
        design_notes="Clean design",
        section_reference="main"
    )
    
    # Test image generation prompt
    print("=== Testing Image Generation Prompt ===")
    image_prompt = build_image_generation_prompt(brand_profile, image_req)
    print("Checking for Dutch language instructions...")
    
    if "LANGUAGE: Generate all text content in Dutch (Nederlands)" in image_prompt:
        print("✅ Dutch language instruction found in image prompt!")
    else:
        print("❌ Dutch language instruction NOT found in image prompt")
        print("Prompt preview:")
        print(image_prompt[:500] + "...")
    
    # Test goal processing prompt
    print("\n=== Testing Goal Processing Prompt ===")
    goal_prompt = build_goal_processing_prompt("Create Facebook ad", brand_profile)
    print("Checking for Dutch language note...")
    
    if "(Note: Content should be in Dutch/Nederlands)" in goal_prompt:
        print("✅ Dutch language note found in goal prompt!")
    else:
        print("❌ Dutch language note NOT found in goal prompt")
        print("Prompt preview:")
        print(goal_prompt[:500] + "...")

def test_english_language_support():
    """Test that English is used for global/English brands."""
    
    # Mock brand profile with English locales
    brand_profile = {
        'metadata': {
            'brand_name': 'Global Tech',
            'description': 'Technology solutions',
            'tone_keywords': ['professional', 'innovative'],
            'mission_statement': 'Advancing technology',
            'locales_supported': ['worldwide']
        },
        'color_system': {
            'primary': [{'name': 'Blue', 'hex': '#0066CC'}],
            'secondary': [],
            'accent': [],
            'neutral': []
        }
    }
    
    # Mock image requirement
    image_req = ImageRequirement(
        title="Product Overview",
        purpose="Showcase products",
        content_focus="Technology",
        key_points=["Innovative", "Reliable"],
        design_notes="Modern design",
        section_reference="main"
    )
    
    print("\n=== Testing English/Global Brand ===")
    image_prompt = build_image_generation_prompt(brand_profile, image_req)
    
    if "LANGUAGE: Generate all text content in Dutch" not in image_prompt:
        print("✅ No specific language instruction for global brand (defaults to English)")
    else:
        print("❌ Unexpected language instruction found for global brand")

if __name__ == "__main__":
    print("Testing Language Support in Branded Images Application")
    print("=" * 60)
    
    test_dutch_language_support()
    test_english_language_support()
    
    print("\n" + "=" * 60)
    print("Language support test completed!")
