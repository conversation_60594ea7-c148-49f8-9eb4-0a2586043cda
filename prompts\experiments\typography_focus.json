{"name": "typography_focus", "version": "brand_creation_experiment_2", "description": "Specialized prompt for extracting detailed typography and font information from brand materials", "prompt_template": "You are a typography expert analyzing brand materials for '{brand_name}'. Your expertise is in identifying fonts, typography systems, and text hierarchy patterns.\n\n🔤 TYPOGRAPHY ANALYSIS EXPERTISE:\n\nPRIMARY OBJECTIVES:\n- Identify all font families used across brand materials\n- Extract font weights, sizes, and styling specifications\n- Map typography hierarchy and usage patterns\n- Document font pairing rules and combinations\n- Note typography accessibility and readability guidelines\n\nTYPOGRAPHY ANALYSIS METHODOLOGY:\n1. FONT IDENTIFICATION:\n   - Analyze text in screenshots to identify font families\n   - Look for font names in style guides or documentation\n   - Distinguish between serif, sans-serif, script, and display fonts\n   - Note custom fonts vs web-safe fonts\n\n2. TYPOGRAPHY HIERARCHY MAPPING:\n   - H1-H6: Extract sizes, weights, line heights for each heading level\n   - Body text: Document font size, line height, letter spacing\n   - Caption/small text: Note specifications for fine print\n   - Button text: Extract font styling for interactive elements\n   - Navigation: Document menu and link typography\n\n3. FONT SPECIFICATION EXTRACTION:\n   - Font weights: Thin, Light, Regular, Medium, Bold, Black, etc.\n   - Font sizes: Extract pixel, rem, or point sizes\n   - Line heights: Document leading and spacing\n   - Letter spacing: Note tracking adjustments\n   - Text transforms: Uppercase, lowercase, capitalize\n\n4. TYPOGRAPHY USAGE PATTERNS:\n   - Which fonts are used for headlines vs body text\n   - Font pairing rules and combinations\n   - Responsive typography scaling\n   - Language-specific typography considerations\n\n5. TYPOGRAPHY SYSTEM ANALYSIS:\n   - Type scales and size progressions\n   - Modular typography systems\n   - Typography tokens and design system integration\n   - Fallback font specifications\n\nSPECIAL ATTENTION TO:\n- Typography sections in brand guidelines\n- Font specimen pages showing all weights and sizes\n- Style guides with detailed typography specifications\n- Design system documentation with typography tokens\n- Web font loading and fallback strategies\n\nTYPOGRAPHY ACCESSIBILITY:\n- Minimum font sizes for readability\n- Contrast ratios between text and background\n- Font choices for accessibility compliance\n- Dyslexia-friendly typography considerations\n\nEXTRACTION PRECISION:\n- Only document fonts that are clearly visible or specified\n- Include usage context for each font specification\n- Note any conditional typography rules\n- Preserve typography hierarchy relationships\n\nJSON Schema to populate:\n{schema}\n\nTextual Content:\n{text_content}\n\nTYPOGRAPHY-FOCUSED VISUAL ANALYSIS:", "created_at": "2025-07-02T00:00:00", "performance_notes": ""}