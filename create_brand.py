import argparse
import json
from pathlib import Path

from app.openai_client import OpenAIClient
from app.schemas import BrandProfile
from prompts.prompt_manager import prompt_manager

BRANDS_DIR = Path(__file__).resolve().parent / "brands"


def create_brand_profile(brand_name: str):
    client = OpenAIClient()
    search_results = """... a comprehensive set of rules that ensure a consistent and recognizable image for the company worldwide. ... Key elements ... Logo: The iconic Spencerian script logo ... Color: The vibrant "Coca-Cola Red" ... Typography: Coca-Cola uses a unified font called "TCCC Unity." ... Bottle Design: The "contour" or "hobble skirt" bottle ... Brand Personality: The brand aims to be authentic, optimistic, friendly, and approachable. ... Design Principles: The brand emphasizes "Bold Simplicity" ... Sustainability: ... "World Without Waste" initiative ..."""
    
    brand_profile_schema = {
        "metadata": {
            "brand_id": "string",
            "brand_name": "string",
            "tagline": "string (optional)",
            "description": "string (optional)",
            "tone_keywords": ["list of strings"],
            "mission_statement": "string (optional)",
            "locales_supported": ["list of strings"]
        },
        "logo": {
            "primary": {
                "url": "string (URL)",
                "placement": {
                    "x": "integer",
                    "y": "integer"
                },
                "max_height_px": "integer",
                "min_clearspace_ratio": "float"
            }
        },
        "color_system": {
            "primary": ["list of hex color codes (e.g., #RRGGBB)"],
            "secondary": ["list of hex color codes"],
            "accent": ["list of hex color codes"],
            "neutral": ["list of hex color codes"]
        }
    }

    # Use centralized prompt manager for brand profile creation
    prompt = prompt_manager.get_brand_profile_prompt(brand_name, search_results, brand_profile_schema)
    brand_profile_json = client.generate_json(prompt)
    brand_profile = BrandProfile.model_validate(brand_profile_json)

    brand_id = brand_name.lower().replace(" ", "_").replace("\"", "")
    fp = BRANDS_DIR / f"{brand_id}.json"
    fp.write_text(json.dumps(brand_profile.model_dump(mode='json'), indent=2))

    print(f"Brand profile for {brand_name} created successfully at {fp}")


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("brand_name", help="The name of the brand to create.")
    args = parser.parse_args()

    create_brand_profile(args.brand_name)