import os
import j<PERSON>
from typing import List

import openai
from dotenv import load_dotenv
from tenacity import retry, wait_exponential, stop_after_attempt

class OpenAIClient:
    def __init__(self):
        load_dotenv(override=True)
        api_key = os.environ.get("OPENAI_API_KEY")
        if not api_key:
            raise RuntimeError("OPENAI_API_KEY env var missing")
        openai.api_key = api_key
        # Create persistent client for new architecture components
        self.client = openai.OpenAI()

    @retry(wait=wait_exponential(multiplier=1, min=1, max=20), stop=stop_after_attempt(3))
    def generate_image(self, prompt: str, n: int = 1) -> List[str]:
        """Generate images using ChatGPT's new image generation model gpt-image-1."""
        try:
            resp = self.client.images.generate(
                model="gpt-image-1",
                prompt=prompt,
                n=n,
                size="1024x1024"
            )
            return [d.url for d in resp.data]
        except Exception as e:
            print(f"Error with gpt-image-1 model: {e}")
            # Fallback to default model if gpt-image-1 fails
            print("Falling back to default image generation model...")
            resp = self.client.images.generate(prompt=prompt, n=n, size="1024x1024")
            return [d.url for d in resp.data]

    @retry(wait=wait_exponential(multiplier=1, min=1, max=20), stop=stop_after_attempt(3))
    def generate_image_with_responses_api(self, prompt: str, reference_images_data_uri: List[str] = None) -> str:
        """Generate image using OpenAI Responses API with gpt-4.1 model and image generation tools."""
        print(f"[DEBUG] Using gpt-4.1 model with Responses API for image generation")
        print(f"[DEBUG] Prompt: {prompt[:100]}...")
        print(f"[DEBUG] Reference images: {len(reference_images_data_uri or [])}")

        # Prepare content with text prompt
        content = [{"type": "input_text", "text": prompt}]

        # Add reference images if provided
        for uri in reference_images_data_uri or []:
            content.append({"type": "input_image", "image_url": uri})

        try:
            resp = self.client.responses.create(
                model="gpt-4.1",
                input=[{"role": "user", "content": content}],
                tools=[
                    {
                        "type": "image_generation",
                        "size": "1024x1024",
                        "quality": "high",
                    }
                ],
            )

            print(f"[DEBUG] Response received, type: {type(resp)}")
            print(f"[DEBUG] Response output length: {len(resp.output) if hasattr(resp, 'output') else 'No output attr'}")

            # Extract the base64 image from the response
            image_b64 = None
            for i, output_item in enumerate(resp.output):
                print(f"[DEBUG] Output item {i}: type={output_item.type}")
                if output_item.type == "image_generation_call":
                    image_b64 = output_item.result
                    print(f"[DEBUG] Found image_generation_call, result length: {len(image_b64) if image_b64 else 'None'}")
                    break

            if not image_b64:
                print(f"[ERROR] No image generated by model")
                print(f"[DEBUG] Full response: {resp}")
                return None

            # Convert base64 to data URI
            data_uri = f"data:image/png;base64,{image_b64}"
            print(f"[DEBUG] Successfully generated image, data URI length: {len(data_uri)}")
            return data_uri

        except Exception as exc:
            print(f"[ERROR] Exception in generate_image_with_responses_api: {exc}")
            raise

    @retry(wait=wait_exponential(multiplier=1, min=1, max=20), stop=stop_after_attempt(3))
    def generate_json(self, prompt: str) -> dict:
        resp = self.client.responses.create(
            model="gpt-4.1",
            instructions="You are a helpful assistant designed to output JSON.",
            input=prompt,
            text={"format": {"type": "json_object"}},
        )
        return json.loads(resp.output_text)

    @retry(wait=wait_exponential(multiplier=1, min=1, max=20), stop=stop_after_attempt(3))
    def analyze_images_and_generate_json(self, base64_images: list, prompt: str) -> dict:
        print(f"[DEBUG] Starting Vision API call...")
        print(f"[DEBUG] Model: gpt-4.1")
        print(f"[DEBUG] Number of images: {len(base64_images)}")
        if base64_images:
            total_image_length = sum(len(img) for img in base64_images)
            print(f"[DEBUG] Total base64 images length: {total_image_length} characters")
            print(f"[DEBUG] First image length: {len(base64_images[0])} characters")
        print(f"[DEBUG] Prompt length: {len(prompt)} characters")

        print(f"[DEBUG] Using persistent OpenAI client")

        # Prepare the content array with text and images
        content = [{"type": "input_text", "text": prompt}]

        # Add each image to the content array (max 8 images)
        for base64_image in base64_images[:8]:
            content.append({
                "type": "input_image",
                "image_url": f"data:image/jpeg;base64,{base64_image}",
            })

        # Prepare the request payload
        request_payload = {
            "model": "gpt-4.1",
            "input": [
                {
                    "role": "user",
                    "content": content,
                }
            ],
            "text": {"format": {"type": "json_object"}},
            "max_output_tokens": 2000,
        }

        print(f"[DEBUG] Request payload structure prepared")
        print(f"[DEBUG] Input content items: {len(request_payload['input'][0]['content'])}")
        print(f"[DEBUG] Input text length: {len(request_payload['input'][0]['content'][0]['text'])}")

        # Log image information
        image_count = len(request_payload['input'][0]['content']) - 1  # Subtract 1 for the text item
        print(f"[DEBUG] Number of images in payload: {image_count}")
        for i in range(1, len(request_payload['input'][0]['content'])):  # Start from 1 to skip text
            image_url_length = len(request_payload['input'][0]['content'][i]['image_url'])
            print(f"[DEBUG] Image {i} URL length: {image_url_length}")

        print(f"[DEBUG] Text format: {request_payload['text']}")
        print(f"[DEBUG] Max output tokens: {request_payload['max_output_tokens']}")

        try:
            print(f"[DEBUG] Making API call to OpenAI Responses API...")
            response = self.client.responses.create(**request_payload)
            print(f"[DEBUG] API call successful!")
            print(f"[DEBUG] Response type: {type(response)}")

            if hasattr(response, "output_text"):
                print(f"[DEBUG] Response output_text length: {len(response.output_text)}")
                print(f"[DEBUG] Response output_text preview: {response.output_text[:200]}...")
                return json.loads(response.output_text)
            else:
                print(f"[ERROR] Response object does not have 'output_text' attribute")
                print(f"[DEBUG] Full response: {response}")
                raise AttributeError("Response object missing 'output_text' attribute")

        except Exception as e:
            print(f"[ERROR] Exception during API call: {type(e).__name__}: {e}")
            print(f"[DEBUG] Exception details: {str(e)}")
            raise
