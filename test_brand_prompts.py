#!/usr/bin/env python3
"""
Test script for brand creation prompts integration.
Verifies that all brand creation prompts work correctly with the centralized system.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from prompts.prompt_manager import prompt_manager

def test_brand_analysis_prompt():
    """Test brand analysis prompt generation."""
    print("Testing brand analysis prompt...")
    
    brand_name = "Test Brand"
    text_content = [
        "Test Brand is a modern technology company focused on innovation.",
        "Our mission is to create cutting-edge solutions for businesses."
    ]
    
    # Mock schema
    schema = {
        "metadata": {
            "brand_name": "string",
            "description": "string"
        },
        "color_system": {
            "primary": ["list of colors"]
        }
    }
    
    try:
        prompt = prompt_manager.get_brand_analysis_prompt(brand_name, text_content, schema)
        
        # Verify prompt contains expected elements
        assert brand_name in prompt
        assert "comprehensive brand details" in prompt
        assert "screenshots" in prompt
        assert "JSON Schema" in prompt
        
        print("✓ Brand analysis prompt generated successfully")
        print(f"  Prompt length: {len(prompt)} characters")
        return True
        
    except Exception as e:
        print(f"✗ Brand analysis prompt failed: {e}")
        return False


def test_brand_profile_prompt():
    """Test brand profile creation prompt."""
    print("Testing brand profile prompt...")
    
    brand_name = "Test Brand"
    search_results = "Test Brand is known for its innovative approach to technology..."
    
    schema = {
        "metadata": {"brand_name": "string"},
        "color_system": {"primary": ["colors"]}
    }
    
    try:
        prompt = prompt_manager.get_brand_profile_prompt(brand_name, search_results, schema)
        
        # Verify prompt contains expected elements
        assert brand_name in prompt
        assert search_results in prompt
        assert "comprehensive brand profile" in prompt
        assert "JSON schema" in prompt
        
        print("✓ Brand profile prompt generated successfully")
        print(f"  Prompt length: {len(prompt)} characters")
        return True
        
    except Exception as e:
        print(f"✗ Brand profile prompt failed: {e}")
        return False


def test_simple_brand_prompt():
    """Test simple brand creation prompt."""
    print("Testing simple brand prompt...")
    
    brand_name = "Test Brand"
    
    try:
        prompt = prompt_manager.get_simple_brand_prompt(brand_name)
        
        # Verify prompt contains expected elements
        assert brand_name in prompt
        assert "brand profile" in prompt
        assert "JSON format" in prompt
        assert "metadata" in prompt
        
        print("✓ Simple brand prompt generated successfully")
        print(f"  Prompt length: {len(prompt)} characters")
        return True
        
    except Exception as e:
        print(f"✗ Simple brand prompt failed: {e}")
        return False


def test_prompt_versions():
    """Test that brand creation version is included in prompt versions."""
    print("Testing prompt versions...")
    
    try:
        versions = prompt_manager.get_prompt_versions()
        
        # Verify brand creation version is included
        assert "brand_creation" in versions
        assert versions["brand_creation"] == "v1.0"
        
        print("✓ Brand creation version found in prompt versions")
        print(f"  Brand creation version: {versions['brand_creation']}")
        return True
        
    except Exception as e:
        print(f"✗ Prompt versions test failed: {e}")
        return False


def test_brand_experiment():
    """Test creating and using a brand creation experiment."""
    print("Testing brand creation experiment...")
    
    try:
        # Create a test experiment
        experiment_name = "enhanced_brand_analysis_test"
        prompt_manager.create_experiment(
            prompt_type="brand_creation",
            name=experiment_name,
            description="Test experiment for brand analysis",
            prompt_template="Test experimental prompt for {brand_name}: {text_content}"
        )
        
        # Test using the experiment
        prompt = prompt_manager.get_brand_analysis_prompt(
            brand_name="Test Brand",
            text_content=["test content"],
            schema={"test": "schema"},
            experiment_name=experiment_name
        )
        
        # Verify experimental prompt is used
        assert "Test experimental prompt" in prompt
        assert "Test Brand" in prompt
        
        print("✓ Brand creation experiment created and used successfully")
        return True
        
    except Exception as e:
        print(f"✗ Brand experiment test failed: {e}")
        return False


def main():
    """Run all brand creation prompt tests."""
    print("=" * 60)
    print("BRAND CREATION PROMPTS INTEGRATION TEST")
    print("=" * 60)
    
    tests = [
        test_brand_analysis_prompt,
        test_brand_profile_prompt,
        test_simple_brand_prompt,
        test_prompt_versions,
        test_brand_experiment
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 60)
    print(f"RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All brand creation prompt tests PASSED!")
        return True
    else:
        print("❌ Some brand creation prompt tests FAILED!")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
