# Prompt Management System Usage Guide

## Overview

The prompt management system provides centralized control over all AI prompts used in the branded images application. This system enables:

- **Centralized Prompt Storage**: All prompts are organized in the `prompts/` package
- **Version Control**: Track prompt versions and changes over time
- **A/B Testing**: Run experiments to compare different prompt variations
- **Easy Maintenance**: Update prompts without touching application code

## Quick Start

### Using Prompts in Your Code

```python
from prompts.prompt_manager import prompt_manager

# Article analysis
prompt = prompt_manager.get_article_analysis_prompt(
    content_type="article",
    content="Your article content here"
)

# Goal processing
prompt = prompt_manager.get_goal_processing_prompt(
    goal_description="Create LinkedIn carousel",
    brand_profile=brand_profile,
    article_content=article_content
)

# Image generation
prompt = prompt_manager.get_image_generation_prompt(
    brand_profile=brand_profile,
    image_req=image_requirement,
    article_content=article_content
)

# Brand analysis (vision-based with screenshots)
prompt = prompt_manager.get_brand_analysis_prompt(
    brand_name="Brand Name",
    text_content=["text content list"],
    schema=brand_schema
)

# Brand profile creation (text-based research)
prompt = prompt_manager.get_brand_profile_prompt(
    brand_name="Brand Name",
    search_results="Research results text",
    schema=brand_schema
)

# Simple brand creation
prompt = prompt_manager.get_simple_brand_prompt(
    brand_name="Brand Name"
)
```

### Running Experiments

```python
# Create a new experiment
prompt_manager.create_experiment(
    name="enhanced_sections_v1",
    prompt_type="article_analysis",
    description="Better section detection",
    prompt_content="Your experimental prompt here..."
)

# Use experimental prompt
prompt = prompt_manager.get_article_analysis_prompt(
    content_type="article",
    content="Content here",
    experiment_name="enhanced_sections_v1"
)

# Add performance feedback
prompt_manager.add_performance_note(
    experiment_name="enhanced_sections_v1",
    note="Improved section detection by 25%"
)
```

## File Structure

```
prompts/
├── __init__.py                 # Package initialization
├── prompt_manager.py           # Main PromptManager class
├── article_analysis.py         # Article analysis prompts
├── goal_processing.py          # Goal processing prompts
├── image_generation.py         # Image generation prompts
├── prompt_tester.py           # Testing utilities
├── experiments/               # Experimental prompts
│   ├── README.md             # Experiment documentation
│   └── *.json                # Individual experiments
└── USAGE_GUIDE.md            # This file
```

## Prompt Types

### 1. Article Analysis Prompts
- **Purpose**: Extract structured information from articles
- **Input**: Content type and raw content
- **Output**: JSON with title, summary, key points, themes, sections
- **Version**: v2.0

### 2. Goal Processing Prompts
- **Purpose**: Convert user goals into structured image requirements
- **Input**: Goal description, brand profile, optional article content
- **Output**: JSON with content type, image count, requirements
- **Version**: v2.1

### 3. Image Generation Prompts
- **Purpose**: Create detailed prompts for AI image generation
- **Input**: Brand profile, image requirements, article content
- **Output**: Comprehensive image generation prompt
- **Version**: v2.2

### 4. Brand Creation Prompts
- **Purpose**: Extract brand information and create brand profiles
- **Types**:
  - **Brand Analysis**: Vision-based analysis with screenshots and text
  - **Brand Profile**: Text-based brand profile creation from research
  - **Simple Brand**: Basic brand profile creation from brand name
- **Input**: Brand name, text content, research results, schemas
- **Output**: Structured prompts for brand information extraction
- **Version**: v1.0

## Creating Experiments

### 1. Manual Experiment Creation

Create a JSON file in `prompts/experiments/`:

```json
{
  "name": "experiment_name",
  "description": "What this experiment tests",
  "prompt_type": "article_analysis",
  "created_date": "2025-07-01",
  "version": "v2.1",
  "changes": ["List of changes made"],
  "prompt_content": "Your experimental prompt...",
  "performance_notes": [],
  "test_results": []
}
```

### 2. Programmatic Experiment Creation

```python
prompt_manager.create_experiment(
    name="better_sections",
    prompt_type="article_analysis",
    description="Improved section detection",
    prompt_content="Enhanced prompt content..."
)
```

## Testing Prompts

### Using the Prompt Tester

```python
from prompts.prompt_tester import prompt_tester

# Test article analysis with multiple experiments
results = prompt_tester.test_article_analysis_prompt(
    content="Your test content",
    experiment_names=["enhanced_sections_v1", "better_themes_v1"]
)

# Compare results
comparison = prompt_tester.compare_results(results)
print(comparison)

# Save test session
prompt_tester.save_test_session("section_detection_test", results)
```

## Best Practices

### 1. Prompt Development
- Start with the baseline prompt
- Make incremental changes
- Test thoroughly before deploying
- Document all changes

### 2. Experimentation
- Use descriptive experiment names
- Include clear descriptions of what you're testing
- Track performance metrics
- Keep experiments focused on specific improvements

### 3. Version Management
- Update version numbers when making significant changes
- Document changes in the changelog
- Keep backward compatibility when possible

### 4. Performance Tracking
- Add performance notes to experiments
- Use consistent testing methodology
- Compare against baseline performance
- Document both successes and failures

## Migration from Old System

The application has been updated to use the centralized prompt system:

- `app/article_processor.py`: Now uses `prompt_manager.get_article_analysis_prompt()`
- `app/goal_processor.py`: Now uses `prompt_manager.get_goal_processing_prompt()`
- `app/image_generator.py`: Now uses `prompt_manager.get_image_generation_prompt()`

All existing functionality remains the same, but prompts are now centrally managed and can be easily experimented with.

## Troubleshooting

### Common Issues

1. **Import Errors**: Ensure the `prompts` package is in your Python path
2. **Experiment Not Found**: Check that experiment files exist in `prompts/experiments/`
3. **JSON Errors**: Validate experiment JSON files for syntax errors
4. **Version Conflicts**: Check prompt versions match expected formats

### Getting Help

- Check the experiment README: `prompts/experiments/README.md`
- Review example experiments in the experiments directory
- Use the test script: `test_prompt_integration.py`
- Check diagnostics for syntax errors

## Future Enhancements

Planned improvements to the prompt management system:

- Web UI for prompt management
- Automated A/B testing
- Performance analytics dashboard
- Integration with version control
- Prompt optimization suggestions
