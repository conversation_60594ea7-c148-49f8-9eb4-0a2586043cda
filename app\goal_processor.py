"""
Flexible goal processing system that can interpret any user-defined goal
and translate it to appropriate image generation requirements.
"""

import json
from typing import Dict, List, Optional, Union, TYPE_CHECKING
from dataclasses import dataclass

from .openai_client import OpenAIClient
from prompts.prompt_manager import prompt_manager

if TYPE_CHECKING:
    from .article_processor import ArticleContent


@dataclass
class ImageRequirement:
    """Represents requirements for a single image."""
    title: str  # Title for the image (often from article section)
    purpose: str  # e.g., "title slide", "content slide 1", "hero image"
    content_focus: str  # What the image should focus on
    design_notes: str  # Additional design considerations
    key_points: List[str]
    section_reference: Optional[str] = None  # Reference to which article section this image represents


@dataclass
class GoalRequirements:
    """Represents the complete requirements for a user goal."""
    goal_description: str
    content_type: str  # e.g., "social_media", "presentation", "marketing"
    image_count: int
    images: List[ImageRequirement]
    overall_style: str
    brand_elements_needed: List[str]
    additional_notes: str


class GoalProcessor:
    """
    Processes flexible user-defined goals and translates them to image generation requirements.
    """
    
    def __init__(self):
        self.openai_client = OpenAIClient()
    
    def process_goal(self, goal_description: str, brand_profile: Dict, article_content: Optional['ArticleContent'] = None) -> GoalRequirements:
        """
        Process a user-defined goal and return structured requirements.

        Args:
            goal_description: Natural language description of the goal
            brand_profile: Brand profile dictionary containing brand information
            article_content: Optional processed article content to enhance goal analysis

        Returns:
            GoalRequirements object with structured image generation requirements
        """
        print(f"Processing goal: {goal_description}")
        if article_content:
            print(f"Using article content: {article_content.title}")

        # Use AI to interpret the goal and generate requirements
        requirements_json = self._analyze_goal_with_ai(goal_description, brand_profile, article_content)

        if not requirements_json:
            # Fallback to basic single image if AI analysis fails
            return self._create_fallback_requirements(goal_description)

        # Convert JSON to GoalRequirements object
        return self._json_to_requirements(requirements_json, goal_description)
    
    def _analyze_goal_with_ai(self, goal_description: str, brand_profile: Dict, article_content: Optional['ArticleContent'] = None) -> Optional[Dict]:
        """Use AI to analyze the goal and generate structured requirements."""

        # Use centralized prompt manager
        prompt = prompt_manager.get_goal_processing_prompt(goal_description, brand_profile, article_content)

        try:
            # Use the OpenAI client to analyze the goal
            response = self.openai_client.client.chat.completions.create(
                model="gpt-4o",
                messages=[
                    {"role": "system", "content": "You are an expert in content creation and visual design. Analyze user goals and provide structured requirements for image generation. Always respond with valid JSON only."},
                    {"role": "user", "content": prompt}
                ],
                response_format={"type": "json_object"}
            )
            
            response_text = response.choices[0].message.content.strip()
            
            # Try to extract JSON from the response
            if response_text.startswith('```json'):
                response_text = response_text[7:-3].strip()
            elif response_text.startswith('```'):
                response_text = response_text[3:-3].strip()

            parsed_response = json.loads(response_text)
            print(f"[DEBUG] AI response Goal Processor: {parsed_response}")
            print(f"[DEBUG] Images in response: {len(parsed_response.get('images', []))}")

            return parsed_response
            
        except Exception as e:
            print(f"Error analyzing goal with AI: {e}")
            return None
    
    def _create_fallback_requirements(self, goal_description: str) -> GoalRequirements:
        """Create basic fallback requirements if AI analysis fails."""
        print("Using fallback requirements for goal processing")
        
        return GoalRequirements(
            goal_description=goal_description,
            content_type="general",
            image_count=1,
            images=[
                ImageRequirement(
                    title="Placeholder Title",
                    purpose="Main image",
                    content_focus=goal_description,
                    design_notes="Create an on-brand image that addresses the goal",
                    key_points=["Placeholder key points"],
                    section_reference=None
                )
            ],
            overall_style="Clean and professional, following brand guidelines",
            brand_elements_needed=["logo", "brand_colors", "typography"],
            additional_notes="Fallback requirements - consider refining the goal description"
        )
    
    def _json_to_requirements(self, requirements_json: Dict, goal_description: str) -> GoalRequirements:
        """Convert JSON requirements to GoalRequirements object."""
        try:
            images = []
            raw_images = requirements_json.get("images", [])
            print(f"[DEBUG] Converting {len(raw_images)} images from JSON to requirements")

            for i, img_data in enumerate(raw_images):
                section_ref = img_data.get("section_reference")
                print(f"[DEBUG] Image {i+1}: {img_data.get('purpose', 'No purpose')} (Section: {section_ref or 'None'})")
                images.append(ImageRequirement(
                    purpose=img_data.get("purpose", "Image"),
                    title=img_data.get("title", "Placeholder Title"),
                    content_focus=img_data.get("content_focus", "Brand content"),
                    design_notes=img_data.get("design_notes", ""),
                    key_points=img_data.get("key_points", ["Placeholder key points"]),
                    section_reference=section_ref
                ))
            
            goal_requirements = GoalRequirements(
                goal_description=goal_description,
                content_type=requirements_json.get("content_type", "general"),
                image_count=requirements_json.get("image_count", len(images)),
                images=images,
                overall_style=requirements_json.get("overall_style", "Professional and on-brand"),
                brand_elements_needed=requirements_json.get("brand_elements_needed", ["logo", "brand_colors"]),
                additional_notes=requirements_json.get("additional_notes", "")
            )

            print(f"[DEBUG] Created GoalRequirements with {len(goal_requirements.images)} images")
            return goal_requirements
            
        except Exception as e:
            print(f"Error converting JSON to requirements: {e}")
            return self._create_fallback_requirements(goal_description)


def process_goal(goal_description: str, brand_profile: Dict, article_content: Optional['ArticleContent'] = None) -> GoalRequirements:
    """
    Convenience function for goal processing.

    Args:
        goal_description: Natural language description of the goal
        brand_profile: Brand profile dictionary containing brand information
        article_content: Optional processed article content to enhance goal analysis

    Returns:
        GoalRequirements object with structured image generation requirements
    """
    processor = GoalProcessor()
    return processor.process_goal(goal_description, brand_profile, article_content)
