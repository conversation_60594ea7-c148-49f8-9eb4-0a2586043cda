"""
Goal-agnostic brand research module.
This module focuses on comprehensive brand profiling regardless of intended use case.
"""

import os
import json
import base64
from typing import List, Dict, Tuple, Optional
from urllib.parse import urlparse

from .search_utils import search_brand_urls, extract_text_content, get_page_title
from .web_scraper import get_screenshot_as_base64
from .openai_client import OpenAIClient
from prompts.prompt_manager import prompt_manager


class BrandResearcher:
    """
    Goal-agnostic brand researcher that creates comprehensive brand profiles.
    """
    
    def __init__(self):
        self.openai_client = OpenAIClient()
    
    def research_brand(self, brand_name: str, force_refresh: bool = False) -> Dict:
        """
        Conduct comprehensive brand research and return complete brand profile.
        
        Args:
            brand_name: Name of the brand to research
            force_refresh: If True, conduct fresh research even if cached profile exists
            
        Returns:
            Complete brand profile dictionary
        """
        print(f"Starting comprehensive brand research for: {brand_name}")
        
        # Check if brand profile already exists (unless force refresh)
        brand_profile_path = self._get_brand_profile_path(brand_name)
        if not force_refresh and os.path.exists(brand_profile_path):
            print(f"Loading existing brand profile from: {brand_profile_path}")
            with open(brand_profile_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        
        # Conduct fresh research
        print("Conducting fresh brand research...")
        
        # Step 1: Search for comprehensive brand information
        urls = self._search_brand_information(brand_name)
        if not urls:
            print("No relevant URLs found. Cannot proceed with research.")
            return {}
        
        # Step 2: Extract content and capture screenshots
        content_data = self._extract_content_and_screenshots(brand_name, urls)
        if not content_data['text_content'] and not content_data['screenshots']:
            print("No data was captured. Cannot proceed with analysis.")
            return {}
        
        # Step 3: Generate comprehensive brand profile
        brand_profile = self._generate_brand_profile(
            brand_name, 
            content_data['text_content'], 
            content_data['screenshots']
        )
        
        # Step 4: Save brand profile for future use
        self._save_brand_profile(brand_name, brand_profile)
        
        print(f"Brand research completed for: {brand_name}")
        return brand_profile
    
    def _search_brand_information(self, brand_name: str) -> List[str]:
        """Search for comprehensive brand information using goal-agnostic queries."""
        print(f"Searching for comprehensive brand information about: {brand_name}")
        
        # Use default comprehensive search (no custom queries)
        urls = search_brand_urls(brand_name, max_results=12)
        print(f"Found {len(urls)} relevant URLs for processing")
        
        return urls
    
    def _extract_content_and_screenshots(self, brand_name: str, urls: List[str]) -> Dict:
        """Extract text content and capture screenshots from URLs."""
        text_content = []
        screenshots_base64 = []
        processed_urls = set()
        
        screenshot_index = 1
        max_screenshots = 10  # Reasonable limit for comprehensive research
        
        for url in urls:
            if url in processed_urls or len(screenshots_base64) >= max_screenshots:
                continue
                
            print(f"Processing URL: {url}")
            processed_urls.add(url)
            
            try:
                # Extract text content
                content = extract_text_content(url)
                if content and len(content.strip()) > 100:
                    title = get_page_title(url)
                    text_content.append(f"Source: {title} ({url})\n{content}")
                    print(f"Extracted text content from: {url}")
                
                # Capture screenshot
                screenshot_base64 = get_screenshot_as_base64(url)
                if screenshot_base64:
                    screenshots_base64.append(screenshot_base64)
                    self._save_screenshot(screenshot_base64, url, brand_name, screenshot_index)
                    screenshot_index += 1
                    print(f"Captured screenshot from: {url}")
                
            except Exception as e:
                print(f"Error processing {url}: {e}")
                continue
        
        print(f"Content extraction completed:")
        print(f"  - Text content sources: {len(text_content)}")
        print(f"  - Screenshots captured: {len(screenshots_base64)}")
        
        return {
            'text_content': text_content,
            'screenshots': screenshots_base64
        }
    
    def _generate_brand_profile(self, brand_name: str, text_content: List[str], screenshots: List[str]) -> Dict:
        """Generate comprehensive brand profile using AI analysis."""
        print("Generating comprehensive brand profile...")
        
        # Create comprehensive analysis prompt
        prompt = self._create_comprehensive_analysis_prompt(brand_name, text_content)
        
        try:
            brand_profile = self.openai_client.analyze_images_and_generate_json(screenshots, prompt)
            print("Brand profile generation completed successfully")
            return brand_profile
            
        except Exception as e:
            print(f"Error generating brand profile: {e}")
            return {}
    
    def _create_comprehensive_analysis_prompt(self, brand_name: str, text_content: List[str]) -> str:
        """Create prompt for comprehensive brand analysis."""
        # Import the comprehensive schema
        from create_brand_agent import COMPREHENSIVE_BRAND_SCHEMA
        
        # Use centralized prompt manager
        return prompt_manager.get_brand_analysis_prompt(brand_name, text_content, COMPREHENSIVE_BRAND_SCHEMA)
    
    def _save_screenshot(self, base64_image: str, url: str, brand_name: str, index: int) -> str:
        """Save a base64 screenshot as a PNG file."""
        try:
            screenshots_dir = f"screenshots/{brand_name.lower().replace(' ', '_')}"
            os.makedirs(screenshots_dir, exist_ok=True)
            
            parsed_url = urlparse(url)
            domain = parsed_url.netloc.replace('www.', '').replace('.', '_')
            filename = f"{index:02d}_{domain}.png"
            filepath = os.path.join(screenshots_dir, filename)
            
            image_data = base64.b64decode(base64_image)
            with open(filepath, 'wb') as f:
                f.write(image_data)
            
            print(f"Screenshot saved: {filepath}")
            return filepath
            
        except Exception as e:
            print(f"Failed to save screenshot for {url}: {e}")
            return None
    
    def _get_brand_profile_path(self, brand_name: str) -> str:
        """Get the file path for a brand profile."""
        brand_id = brand_name.lower().replace(' ', '_').replace('"', '')
        return f"brands/{brand_id}_comprehensive.json"
    
    def _save_brand_profile(self, brand_name: str, brand_profile: Dict) -> None:
        """Save brand profile to file."""
        os.makedirs("brands", exist_ok=True)
        profile_path = self._get_brand_profile_path(brand_name)
        
        with open(profile_path, 'w', encoding='utf-8') as f:
            json.dump(brand_profile, f, indent=2, ensure_ascii=False)
        
        print(f"Brand profile saved: {profile_path}")


def research_brand(brand_name: str, force_refresh: bool = False) -> Dict:
    """
    Convenience function for brand research.
    
    Args:
        brand_name: Name of the brand to research
        force_refresh: If True, conduct fresh research even if cached profile exists
        
    Returns:
        Complete brand profile dictionary
    """
    researcher = BrandResearcher()
    return researcher.research_brand(brand_name, force_refresh)
