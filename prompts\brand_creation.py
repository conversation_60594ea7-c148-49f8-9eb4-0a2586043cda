"""
Brand Creation Prompts
Centralized prompts for brand research, analysis, and profile generation.
"""

import json
from typing import List, Dict, Any

# Version tracking
BRAND_CREATION_VERSION = "v1.0"

def build_brand_analysis_prompt(brand_name: str, text_content: List[str], schema: Dict[str, Any]) -> str:
    """Build comprehensive brand analysis prompt for vision API with screenshots."""

    return f"""Given the following text content and visual information (multiple screenshots) related to the brand '{brand_name}', extract comprehensive brand details. Analyze ALL provided screenshots along with the text content to populate the JSON schema with as much detail as possible.

CRITICAL DESIGN ANALYSIS FOCUS:
Examine each screenshot meticulously for the following design elements:

🎨 COLOR SYSTEM EXTRACTION:
- Identify ALL colors used in the brand (backgrounds, text, buttons, accents)
- Extract exact hex codes when visible in style guides or color palettes
- Note color names if provided (e.g., "Brand Blue", "Primary Red")
- Include RGB values if available
- Categorize colors as primary, secondary, accent, or neutral based on usage frequency and prominence
- Look for gradient definitions and color usage guidelines
- Note accessibility information if mentioned

🔤 TYPOGRAPHY DEEP ANALYSIS:
- Identify font families used for headings, body text, buttons, captions
- Extract font weights (Light, Regular, Medium, Bold, Black, etc.)
- Note font sizes for different text hierarchies (H1-H6, body, caption)
- Identify line heights and letter spacing if visible
- Look for typography scales or size systems
- Note fallback fonts mentioned in documentation
- Extract usage guidelines for each font (when to use what)

📐 LAYOUT & SPACING SYSTEMS:
- Identify grid systems (number of columns, gutter widths)
- Extract spacing units and scales (8px, 16px, 24px systems)
- Note margin and padding patterns
- Identify breakpoints for responsive design
- Look for layout principles and guidelines

🖼️ VISUAL STYLE PATTERNS:
- Analyze iconography style (outline, filled, stroke width, corner radius)
- Identify photography and illustration styles
- Note image treatment patterns (filters, overlays, aspect ratios)
- Extract border radius values for buttons and cards
- Identify shadow styles and elevation systems

📋 BRAND GUIDELINES EXTRACTION:
- Look for logo usage rules (minimum sizes, clearspace, do's and don'ts)
- Extract brand voice and tone descriptions
- Note any specific design principles or philosophies
- Identify brand values and positioning statements

EXTRACTION GUIDELINES:
- Cross-reference information between text content and visual elements in screenshots
- If information is not explicitly found, set the corresponding field to null or empty array/object
- Do NOT make up information - only extract what is clearly visible or stated
- Pay special attention to style guides, brand books, and design system documentation
- Extract exact measurements, sizes, and specifications when visible
- Note context and usage rules for design elements
- Prioritize official brand documentation over general website content

Extract as much detail as possible from both text and visual content. If information is not available, use null or empty arrays as appropriate.

JSON Schema to populate:
{json.dumps(schema, indent=2)}

Textual Content:
{chr(10).join(text_content)}

Visual Analysis (analyze all provided screenshots for comprehensive brand insights):"""


def build_brand_profile_prompt(brand_name: str, search_results: str, schema: Dict[str, Any]) -> str:
    """Build brand profile creation prompt for text-based brand research."""
    
    return f"""Create a comprehensive brand profile for {brand_name} based on the following research information.

BRAND: {brand_name}

RESEARCH INFORMATION:
{search_results}

INSTRUCTIONS:
- Analyze the provided information to extract brand details
- Fill in the JSON schema with accurate information found in the research
- For fields where information is not explicitly available, use your knowledge to make reasonable inferences
- Leave fields as null or empty arrays when no information is available
- Ensure all color codes are in hexadecimal format (e.g., #RRGGBB)
- Focus on creating a comprehensive and accurate brand profile

BRAND PROFILE SCHEMA:
{json.dumps(schema, indent=2)}

Please fill in the details for {brand_name} based on the provided information. For fields where information is not explicitly available, use your available knowledge to fill them in or leave them as empty lists/None as appropriate. Ensure all color codes are in hexadecimal format (e.g., #RRGGBB).

Provide only the JSON response:"""


def build_design_focused_analysis_prompt(brand_name: str, text_content: List[str], schema: Dict[str, Any]) -> str:
    """Build design-focused brand analysis prompt specifically for extracting visual design elements."""

    return f"""You are a brand design expert analyzing visual content for '{brand_name}'. Your task is to extract detailed design system information from the provided screenshots and text content.

SPECIALIZED DESIGN EXTRACTION MISSION:
Focus exclusively on visual design elements, typography, colors, and layout systems. Ignore general marketing content unless it contains specific design specifications.

🎯 PRIMARY EXTRACTION TARGETS:

COLOR SYSTEM ANALYSIS:
- Scan for color palettes, swatches, or color specifications
- Extract hex codes, RGB values, and color names
- Identify color usage patterns (primary for headers, secondary for backgrounds, etc.)
- Look for accessibility contrast ratios or WCAG compliance notes
- Note any gradient definitions or color mixing rules

TYPOGRAPHY SYSTEM ANALYSIS:
- Identify all font families used across different contexts
- Extract font weights, sizes, and line heights from style guides
- Map typography hierarchy (H1-H6, body, caption, button text)
- Look for font pairing rules and usage guidelines
- Note any custom fonts or web font specifications

LAYOUT & SPACING ANALYSIS:
- Identify grid systems and column structures
- Extract spacing scales (4px, 8px, 16px, 24px, 32px systems)
- Note margin and padding specifications
- Look for responsive breakpoints and layout rules
- Identify alignment and positioning guidelines

COMPONENT DESIGN ANALYSIS:
- Extract button styles (border radius, padding, hover states)
- Identify card and container styling patterns
- Note iconography style (stroke width, corner radius, style consistency)
- Look for form element styling specifications
- Extract navigation and UI component patterns

BRAND ASSET ANALYSIS:
- Identify logo variations and usage contexts
- Extract logo specifications (minimum sizes, clearspace ratios)
- Note brand mark and wordmark usage rules
- Look for co-branding or partnership logo guidelines

VISUAL STYLE ANALYSIS:
- Identify photography style and treatment guidelines
- Note illustration style and usage patterns
- Extract image aspect ratios and cropping guidelines
- Look for overlay and filter specifications

EXTRACTION PRECISION REQUIREMENTS:
- Extract exact numerical values (px, rem, %, ratios)
- Note specific color codes and names
- Include usage context for each design element
- Preserve hierarchical relationships between elements
- Document any conditional usage rules or exceptions

JSON Schema to populate:
{json.dumps(schema, indent=2)}

Textual Content:
{chr(10).join(text_content)}

VISUAL DESIGN ANALYSIS (focus on design systems, style guides, and visual specifications):"""


def build_simple_brand_prompt(brand_name: str) -> str:
    """Build a simple brand profile creation prompt for basic brand information."""
    
    return f"""Create a basic brand profile for {brand_name} using your knowledge of this brand.

BRAND: {brand_name}

Please provide the following information in JSON format:
{{
    "metadata": {{
        "brand_id": "string - lowercase with underscores",
        "brand_name": "string - official brand name",
        "tagline": "string - brand tagline or slogan (null if unknown)",
        "description": "string - brief brand description",
        "tone_keywords": ["list", "of", "brand", "tone", "keywords"],
        "mission_statement": "string - brand mission (null if unknown)",
        "locales_supported": ["list", "of", "supported", "locales"]
    }},
    "logo": {{
        "primary": {{
            "url": "string - logo URL (null if unknown)",
            "placement": {{"x": 0, "y": 0}},
            "max_height_px": 100,
            "min_clearspace_ratio": 0.5
        }}
    }},
    "color_system": {{
        "primary": ["#RRGGBB", "hex", "color", "codes"],
        "secondary": ["#RRGGBB", "hex", "color", "codes"],
        "accent": ["#RRGGBB", "hex", "color", "codes"],
        "neutral": ["#RRGGBB", "hex", "color", "codes"]
    }},
    "typography": {{
        "primary_font": {{
            "family": "string - primary font family",
            "usage": "string - usage description"
        }},
        "secondary_font": {{
            "family": "string - secondary font family",
            "usage": "string - usage description"
        }}
    }}
}}

GUIDELINES:
- Use your knowledge of the brand to fill in accurate information
- For unknown fields, use null or empty arrays
- Ensure color codes are valid hex format
- Focus on well-known brand characteristics
- Keep descriptions concise but informative

Provide only the JSON response:"""


# Changelog for brand creation prompts
BRAND_CREATION_CHANGELOG = [
    {
        "version": "v1.0",
        "date": "2025-07-02", 
        "changes": [
            "Initial centralization of brand creation prompts",
            "Created build_brand_analysis_prompt for comprehensive vision-based analysis",
            "Created build_brand_profile_prompt for text-based research",
            "Created build_simple_brand_prompt for basic brand profiles",
            "Added comprehensive extraction guidelines",
            "Standardized JSON schema handling"
        ]
    }
]


def build_website_design_analysis_prompt(brand_name: str, text_content: List[str], schema: Dict) -> str:
    """
    Build a specialized prompt for analyzing design elements from a brand's actual website implementation.
    This focuses on extracting design patterns, colors, typography, and layout from live website screenshots.
    """

    # Convert schema to JSON string for the prompt
    schema_json = json.dumps(schema, indent=2)

    # Combine text content
    combined_content = "\n\n".join(text_content) if text_content else "No text content available."

    prompt = f"""You are a web design expert analyzing the visual implementation of '{brand_name}' from their actual website. Your task is to extract detailed design system information by analyzing the provided website screenshots and content.

WEBSITE DESIGN ANALYSIS METHODOLOGY:

1. VISUAL DESIGN SYSTEM EXTRACTION:
   - Analyze the actual implementation of colors, typography, and layout patterns
   - Extract design elements as they appear in the live website
   - Focus on consistency patterns across different pages
   - Identify the visual hierarchy and design principles in use

2. COLOR SYSTEM ANALYSIS:
   - Extract primary, secondary, and accent colors from the website screenshots
   - Identify color usage patterns (headers, buttons, backgrounds, text)
   - Note any gradients or color variations used
   - Analyze color accessibility and contrast patterns
   - Provide specific hex codes where possible from visual analysis

3. TYPOGRAPHY SYSTEM ANALYSIS:
   - Identify font families used for headings, body text, and UI elements
   - Analyze text hierarchy (H1, H2, H3, body, captions, buttons)
   - Extract font weights, sizes, and spacing patterns
   - Note typography usage guidelines from implementation
   - Identify any custom or distinctive typography choices

4. LAYOUT & SPACING ANALYSIS:
   - Analyze grid systems and layout patterns
   - Extract spacing patterns and rhythm
   - Identify breakpoints and responsive design patterns
   - Note layout principles and design consistency
   - Analyze component spacing and alignment

5. DESIGN TOKENS & PATTERNS:
   - Extract border radius patterns
   - Identify shadow and elevation patterns
   - Note transition and animation patterns
   - Extract button styles and interactive elements
   - Identify iconography and visual elements

6. BRAND IMPLEMENTATION ANALYSIS:
   - How design elements support brand identity
   - Consistency across different page types
   - Visual tone and personality expression
   - User experience design patterns

CONTENT TO ANALYZE:
{combined_content}

IMPORTANT INSTRUCTIONS:
- Focus on what you can actually observe in the website screenshots
- Extract specific values (colors, sizes, spacing) where visible
- Note patterns and consistency across different pages
- Provide detailed analysis of the visual implementation
- Fill in design-related fields with actual observed data
- If certain design information isn't visible, indicate this clearly
- Prioritize accuracy over completeness

Please analyze the provided website content and screenshots to extract comprehensive design system information.

CRITICAL: Return ONLY a valid JSON object following this exact schema. Do not include any explanatory text, markdown formatting, or code blocks. Start your response directly with the opening curly brace {{.

{schema_json}

Ensure all design-related fields (color_system, typography, layout_system, design_tokens) are thoroughly populated based on your analysis of the actual website implementation. Return only the JSON object, nothing else."""

    return prompt
