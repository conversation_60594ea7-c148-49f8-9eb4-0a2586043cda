"""
Brand Creation Prompts
Centralized prompts for brand research, analysis, and profile generation.
"""

import json
from typing import List, Dict, Any

# Version tracking
BRAND_CREATION_VERSION = "v1.0"

def build_brand_analysis_prompt(brand_name: str, text_content: List[str], schema: Dict[str, Any]) -> str:
    """Build comprehensive brand analysis prompt for vision API with screenshots."""
    
    return f"""Given the following text content and visual information (multiple screenshots) related to the brand '{brand_name}', extract comprehensive brand details. Analyze ALL provided screenshots along with the text content to populate the JSON schema with as much detail as possible.

ANALYSIS FOCUS:
- Brand mission, vision, values, target audience, and brand story
- Tone of voice keywords and descriptions  
- Logo details: primary logo URL (if found), descriptions, usage guidelines, and variations
- Color system: primary, secondary, accent, and neutral colors, including hex codes (if visible or mentioned)
- Typography: primary, secondary, and other font families and their usage
- Imagery style: descriptions and examples (if URLs are visible)
- Any other relevant brand asset URLs

EXTRACTION GUIDELINES:
- Cross-reference information between text content and visual elements in screenshots
- If information is not explicitly found, set the corresponding field to null or empty array/object
- Do NOT make up information - only extract what is clearly visible or stated
- Pay special attention to color schemes, fonts, and visual patterns in screenshots
- Look for brand guidelines, style guides, or official brand documentation
- Extract exact color codes when visible (hex format preferred)
- Note font families and typography usage patterns
- Identify logo variations and usage contexts

Extract as much detail as possible from both text and visual content. If information is not available, use null or empty arrays as appropriate.

JSON Schema to populate:
{json.dumps(schema, indent=2)}

Textual Content:
{chr(10).join(text_content)}

Visual Analysis (analyze all provided screenshots for comprehensive brand insights):"""


def build_brand_profile_prompt(brand_name: str, search_results: str, schema: Dict[str, Any]) -> str:
    """Build brand profile creation prompt for text-based brand research."""
    
    return f"""Create a comprehensive brand profile for {brand_name} based on the following research information.

BRAND: {brand_name}

RESEARCH INFORMATION:
{search_results}

INSTRUCTIONS:
- Analyze the provided information to extract brand details
- Fill in the JSON schema with accurate information found in the research
- For fields where information is not explicitly available, use your knowledge to make reasonable inferences
- Leave fields as null or empty arrays when no information is available
- Ensure all color codes are in hexadecimal format (e.g., #RRGGBB)
- Focus on creating a comprehensive and accurate brand profile

BRAND PROFILE SCHEMA:
{json.dumps(schema, indent=2)}

Please fill in the details for {brand_name} based on the provided information. For fields where information is not explicitly available, use your available knowledge to fill them in or leave them as empty lists/None as appropriate. Ensure all color codes are in hexadecimal format (e.g., #RRGGBB).

Provide only the JSON response:"""


def build_simple_brand_prompt(brand_name: str) -> str:
    """Build a simple brand profile creation prompt for basic brand information."""
    
    return f"""Create a basic brand profile for {brand_name} using your knowledge of this brand.

BRAND: {brand_name}

Please provide the following information in JSON format:
{{
    "metadata": {{
        "brand_id": "string - lowercase with underscores",
        "brand_name": "string - official brand name",
        "tagline": "string - brand tagline or slogan (null if unknown)",
        "description": "string - brief brand description",
        "tone_keywords": ["list", "of", "brand", "tone", "keywords"],
        "mission_statement": "string - brand mission (null if unknown)",
        "locales_supported": ["list", "of", "supported", "locales"]
    }},
    "logo": {{
        "primary": {{
            "url": "string - logo URL (null if unknown)",
            "placement": {{"x": 0, "y": 0}},
            "max_height_px": 100,
            "min_clearspace_ratio": 0.5
        }}
    }},
    "color_system": {{
        "primary": ["#RRGGBB", "hex", "color", "codes"],
        "secondary": ["#RRGGBB", "hex", "color", "codes"],
        "accent": ["#RRGGBB", "hex", "color", "codes"],
        "neutral": ["#RRGGBB", "hex", "color", "codes"]
    }},
    "typography": {{
        "primary_font": {{
            "family": "string - primary font family",
            "usage": "string - usage description"
        }},
        "secondary_font": {{
            "family": "string - secondary font family",
            "usage": "string - usage description"
        }}
    }}
}}

GUIDELINES:
- Use your knowledge of the brand to fill in accurate information
- For unknown fields, use null or empty arrays
- Ensure color codes are valid hex format
- Focus on well-known brand characteristics
- Keep descriptions concise but informative

Provide only the JSON response:"""


# Changelog for brand creation prompts
BRAND_CREATION_CHANGELOG = [
    {
        "version": "v1.0",
        "date": "2025-07-02", 
        "changes": [
            "Initial centralization of brand creation prompts",
            "Created build_brand_analysis_prompt for comprehensive vision-based analysis",
            "Created build_brand_profile_prompt for text-based research",
            "Created build_simple_brand_prompt for basic brand profiles",
            "Added comprehensive extraction guidelines",
            "Standardized JSON schema handling"
        ]
    }
]
