#!/usr/bin/env python3
"""
Test script for the new architecture.
This script validates that all components work together correctly.
"""

import sys
from pathlib import Path

# Add the current directory to Python path for imports
sys.path.append(str(Path(__file__).parent))

from app.brand_research import research_brand
from app.goal_processor import process_goal
from app.article_processor import process_content
from app.image_generator import generate_images


def test_brand_research():
    """Test goal-agnostic brand research."""
    print("=" * 60)
    print("🧪 Testing Brand Research (Goal-Agnostic)")
    print("=" * 60)
    
    try:
        # Test with Coca-Cola (should use existing enhanced profile)
        brand_profile = research_brand("Coca-Cola", force_refresh=False)
        
        if brand_profile:
            print("✅ Brand research successful")
            
            # Validate profile structure
            metadata = brand_profile.get('metadata', {})
            print(f"📝 Brand: {metadata.get('brand_name', 'Unknown')}")
            print(f"📝 Description: {metadata.get('description', 'No description')[:100]}...")
            
            # Check color system
            color_system = brand_profile.get('color_system', {})
            primary_colors = color_system.get('primary', [])
            if primary_colors:
                print(f"🎨 Primary colors found: {len(primary_colors)}")
            
            # Check typography
            typography = brand_profile.get('typography', {})
            if typography.get('primary_font'):
                print(f"🔤 Primary font: {typography['primary_font'].get('family', 'Unknown')}")
            
            return brand_profile
        else:
            print("❌ Brand research failed")
            return None
            
    except Exception as e:
        print(f"❌ Error in brand research: {e}")
        return None


def test_goal_processing():
    """Test flexible goal processing."""
    print("\n" + "=" * 60)
    print("🧪 Testing Goal Processing (Flexible)")
    print("=" * 60)
    
    test_goals = [
        "LinkedIn Carousel about sustainability",
        "Instagram Story for product launch",
        "Hero banner for website homepage",
        "Email newsletter header image",
        "Product showcase for e-commerce"
    ]
    
    results = []
    
    for goal in test_goals:
        print(f"\n🎯 Testing goal: {goal}")
        
        try:
            # Get a dummy brand profile for testing
            brand_profile = {"brand_name": "Test Brand", "colors": ["#000000"], "style": "modern"}
            requirements = process_goal(goal, brand_profile)

            print(f"✅ Goal processed successfully")
            print(f"   Content type: {requirements.content_type}")
            print(f"   Image count: {requirements.image_count}")
            print(f"   Overall style: {requirements.overall_style}")

            if requirements.images:
                print(f"   First image purpose: {requirements.images[0].purpose}")
                print(f"   First image dimensions: {requirements.images[0].dimensions}")

            results.append(requirements)
            
        except Exception as e:
            print(f"❌ Error processing goal '{goal}': {e}")
            results.append(None)
    
    return results


def test_article_processing():
    """Test article content processing."""
    print("\n" + "=" * 60)
    print("🧪 Testing Article Processing")
    print("=" * 60)
    
    test_content = """
    Sustainable Innovation at Coca-Cola
    
    At Coca-Cola, we're committed to creating a more sustainable future through innovative packaging solutions. 
    Our new PlantBottle technology represents a breakthrough in sustainable packaging, using up to 30% plant-based materials.
    
    Key benefits:
    - Reduced carbon footprint
    - Renewable materials
    - Same quality and safety
    - Recyclable design
    
    Join us in our mission to refresh the world while protecting our planet for future generations.
    """
    
    try:
        article_content = process_content(test_content, "campaign")
        
        print("✅ Article processing successful")
        print(f"📝 Title: {article_content.title}")
        print(f"📝 Summary: {article_content.summary[:100]}...")
        print(f"🎯 Main themes: {', '.join(article_content.main_themes[:3])}")
        print(f"🎭 Tone: {article_content.tone}")
        print(f"👥 Target audience: {article_content.target_audience}")
        
        if article_content.key_points:
            print(f"📋 Key points: {len(article_content.key_points)}")
            for i, point in enumerate(article_content.key_points[:3], 1):
                print(f"   {i}. {point}")
        
        return article_content
        
    except Exception as e:
        print(f"❌ Error processing article: {e}")
        return None


def test_enhanced_goal_processing():
    """Test enhanced goal processing with article content."""
    print("\n" + "=" * 60)
    print("🧪 Testing Enhanced Goal Processing (with Article Content)")
    print("=" * 60)

    # Test content
    test_content = """
    Sustainable Innovation at Coca-Cola

    At Coca-Cola, we're committed to creating a more sustainable future through innovative packaging solutions.
    Our new PlantBottle technology represents a breakthrough in sustainable packaging, using up to 30% plant-based materials.

    Key benefits:
    - Reduced carbon footprint
    - Renewable materials
    - Same quality and safety
    - Recyclable design

    Join us in our mission to refresh the world while protecting our planet for future generations.
    """

    test_goal = "LinkedIn Carousel about sustainability"

    try:
        # Process article content first
        print(f"📄 Processing article content...")
        article_content = process_content(test_content, "campaign")
        print(f"✅ Article processed: {article_content.title}")

        # Get a dummy brand profile for testing
        brand_profile = {"brand_name": "Test Brand", "colors": ["#000000"], "style": "modern"}

        # Process goal without article content (baseline)
        print(f"\n🎯 Processing goal without article content...")
        goal_requirements_basic = process_goal(test_goal, brand_profile)
        print(f"✅ Basic goal processed: {goal_requirements_basic.content_type}")
        print(f"   Image count: {goal_requirements_basic.image_count}")
        print(f"   Overall style: {goal_requirements_basic.overall_style[:100]}...")

        # Process goal with article content (enhanced)
        print(f"\n🎯 Processing goal WITH article content...")
        goal_requirements_enhanced = process_goal(test_goal, brand_profile, article_content)
        print(f"✅ Enhanced goal processed: {goal_requirements_enhanced.content_type}")
        print(f"   Image count: {goal_requirements_enhanced.image_count}")
        print(f"   Overall style: {goal_requirements_enhanced.overall_style[:100]}...")

        # Compare results
        print(f"\n📊 Comparison:")
        print(f"   Basic vs Enhanced image count: {goal_requirements_basic.image_count} vs {goal_requirements_enhanced.image_count}")
        print(f"   Style difference: {'Yes' if goal_requirements_basic.overall_style != goal_requirements_enhanced.overall_style else 'No'}")

        return goal_requirements_enhanced

    except Exception as e:
        print(f"❌ Error in enhanced goal processing: {e}")
        return None


def test_image_generation(brand_profile, goal_requirements, article_content):
    """Test image generation pipeline."""
    print("\n" + "=" * 60)
    print("🧪 Testing Image Generation Pipeline")
    print("=" * 60)
    
    if not all([brand_profile, goal_requirements, article_content]):
        print("❌ Cannot test image generation - missing prerequisites")
        return None
    
    try:
        print("🎨 Starting image generation...")
        print(f"   Brand: {brand_profile.get('metadata', {}).get('brand_name', 'Unknown')}")
        print(f"   Goal: {goal_requirements.goal_description}")
        print(f"   Article: {article_content.title}")
        print(f"   Images to generate: {goal_requirements.image_count}")
        
        # Note: This will actually call the OpenAI API and generate images
        # Comment out the next line if you don't want to use API credits during testing
        result = generate_images(brand_profile, goal_requirements, article_content)
        
        if result.success:
            print("✅ Image generation successful")
            print(f"📊 Generated {len(result.images)} images")
            
            for i, img in enumerate(result.images, 1):
                print(f"   {i}. {img.purpose}")
                if img.local_path:
                    print(f"      📁 {img.local_path}")
            
            print(f"\n📄 Summary:\n{result.generation_summary}")
            return result
        else:
            print(f"❌ Image generation failed: {result.error_message}")
            return None
            
    except Exception as e:
        print(f"❌ Error in image generation: {e}")
        return None


def main():
    """Run all tests."""
    print("🧪 NEW ARCHITECTURE VALIDATION TEST")
    print("This script tests all components of the new architecture")
    print()
    
    # Test 1: Brand Research
    brand_profile = test_brand_research()

    # Test 2: Goal Processing
    goal_results = test_goal_processing()
    goal_requirements = goal_results[0] if goal_results and goal_results[0] else None

    # Test 3: Article Processing
    article_content = test_article_processing()

    # Test 4: Enhanced Goal Processing (with article content)
    enhanced_goal_requirements = test_enhanced_goal_processing()

    # Test 5: Image Generation (optional - uses API credits)
    print("\n" + "=" * 60)
    print("🧪 Image Generation Test")
    print("=" * 60)
    print("⚠️  Image generation test will use OpenAI API credits.")
    print("⚠️  Comment out the test if you don't want to generate actual images.")
    
    user_input = input("Do you want to run the image generation test? (y/N): ").strip().lower()
    
    if user_input == 'y':
        image_result = test_image_generation(brand_profile, goal_requirements, article_content)
    else:
        print("⏭️  Skipping image generation test")
        image_result = "skipped"
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    tests = [
        ("Brand Research", "✅" if brand_profile else "❌"),
        ("Goal Processing", "✅" if goal_requirements else "❌"),
        ("Article Processing", "✅" if article_content else "❌"),
        ("Enhanced Goal Processing", "✅" if enhanced_goal_requirements else "❌"),
        ("Image Generation", "✅" if image_result and image_result != "skipped" and getattr(image_result, 'success', False) else "⏭️" if image_result == "skipped" else "❌")
    ]
    
    for test_name, status in tests:
        print(f"{status} {test_name}")
    
    all_passed = all(status in ["✅", "⏭️"] for _, status in tests)
    
    if all_passed:
        print("\n🎉 All tests passed! New architecture is working correctly.")
    else:
        print("\n⚠️  Some tests failed. Check the output above for details.")
    
    print("\n📁 Check the following directories for outputs:")
    print("   - brands/ (brand profiles)")
    print("   - screenshots/ (research screenshots)")
    print("   - generated_images/ (generated images)")
    print("   - generation_summaries/ (generation reports)")


if __name__ == "__main__":
    main()
