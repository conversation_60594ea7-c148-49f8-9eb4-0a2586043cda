#!/usr/bin/env python3
"""
Test script for enhanced design-focused brand collection system.
Tests the improved search queries, schema, and analysis prompts.
"""

import json
import os
import pytest
from app.brand_research import <PERSON><PERSON><PERSON>ar<PERSON>

def run_design_focused_research_test(brand_name: str, force_refresh: bool = True):
    """Test the enhanced design-focused brand research."""
    print(f"🧪 Testing enhanced design collection for: {brand_name}")
    print("=" * 60)
    
    try:
        # Initialize brand researcher
        researcher = BrandResearcher()
        
        # Test design-focused research
        print("🔍 Starting design-focused brand research...")
        brand_profile = researcher.research_brand_with_design_focus(
            brand_name, 
            force_refresh=force_refresh
        )
        
        if not brand_profile:
            print("❌ No brand profile generated")
            return False
        
        print("✅ Brand profile generated successfully!")
        
        # Analyze the quality of design data collected
        analyze_design_data_quality(brand_name, brand_profile)
        
        return True
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        return False

def analyze_design_data_quality(brand_name: str, brand_profile: dict):
    """Analyze the quality of design data collected."""
    print("\n📊 DESIGN DATA QUALITY ANALYSIS")
    print("-" * 40)
    
    # Check color system completeness
    color_system = brand_profile.get('color_system', {})
    color_score = 0
    
    primary_colors = color_system.get('primary', [])
    secondary_colors = color_system.get('secondary', [])
    accent_colors = color_system.get('accent', [])
    neutral_colors = color_system.get('neutral', [])
    
    if primary_colors:
        color_score += 25
        print(f"✅ Primary colors found: {len(primary_colors)}")
        # Check if colors have detailed information
        if any(isinstance(color, dict) and 'hex' in color for color in primary_colors):
            color_score += 15
            print("✅ Detailed color information (hex codes) found")
    else:
        print("❌ No primary colors found")
    
    if secondary_colors:
        color_score += 15
        print(f"✅ Secondary colors found: {len(secondary_colors)}")
    
    if accent_colors:
        color_score += 10
        print(f"✅ Accent colors found: {len(accent_colors)}")
    
    if neutral_colors:
        color_score += 10
        print(f"✅ Neutral colors found: {len(neutral_colors)}")
    
    # Check typography completeness
    typography = brand_profile.get('typography', {})
    typography_score = 0
    
    primary_font = typography.get('primary_font', {})
    secondary_font = typography.get('secondary_font', {})
    
    if primary_font and primary_font.get('family'):
        typography_score += 30
        print(f"✅ Primary font found: {primary_font.get('family')}")
        
        if primary_font.get('weights'):
            typography_score += 15
            print(f"✅ Font weights specified: {primary_font.get('weights')}")
        
        if primary_font.get('usage'):
            typography_score += 10
            print("✅ Font usage guidelines found")
    else:
        print("❌ No primary font information found")
    
    if secondary_font and secondary_font.get('family'):
        typography_score += 20
        print(f"✅ Secondary font found: {secondary_font.get('family')}")
    
    # Check layout system
    layout_system = brand_profile.get('layout_system', {})
    layout_score = 0
    
    grid = layout_system.get('grid', {})
    spacing = layout_system.get('spacing', {})
    
    if grid and any(grid.values()):
        layout_score += 25
        print("✅ Grid system information found")
    
    if spacing and any(spacing.values()):
        layout_score += 25
        print("✅ Spacing system information found")
    
    # Check design tokens
    design_tokens = brand_profile.get('design_tokens', {})
    tokens_score = 0
    
    if design_tokens and any(design_tokens.values()):
        tokens_score += 20
        print("✅ Design tokens found")
    
    # Calculate overall design quality score
    total_score = color_score + typography_score + layout_score + tokens_score
    max_score = 100 + 75 + 50 + 20  # Maximum possible scores
    
    quality_percentage = (total_score / max_score) * 100
    
    print(f"\n📈 DESIGN DATA QUALITY SCORES:")
    print(f"   Color System: {color_score}/100")
    print(f"   Typography: {typography_score}/75") 
    print(f"   Layout System: {layout_score}/50")
    print(f"   Design Tokens: {tokens_score}/20")
    print(f"   Overall Quality: {quality_percentage:.1f}%")
    
    # Provide recommendations
    print(f"\n💡 RECOMMENDATIONS:")
    if color_score < 50:
        print("   - Improve color extraction by targeting style guides")
    if typography_score < 40:
        print("   - Focus on typography documentation and font specimens")
    if layout_score < 25:
        print("   - Search for design system and grid documentation")
    if tokens_score < 10:
        print("   - Look for component libraries and design tokens")
    
    return quality_percentage

def compare_with_standard_research(brand_name: str):
    """Compare design-focused research with standard research."""
    print(f"\n🔄 COMPARISON TEST: {brand_name}")
    print("=" * 60)
    
    researcher = BrandResearcher()
    
    # Standard research
    print("🔍 Running standard brand research...")
    standard_profile = researcher.research_brand(brand_name, force_refresh=True)
    
    # Design-focused research  
    print("🎨 Running design-focused brand research...")
    design_profile = researcher.research_brand_with_design_focus(brand_name, force_refresh=True)
    
    if standard_profile and design_profile:
        print("\n📊 COMPARISON RESULTS:")
        
        # Compare color data
        std_colors = len(standard_profile.get('color_system', {}).get('primary', []))
        design_colors = len(design_profile.get('color_system', {}).get('primary', []))
        print(f"   Primary Colors - Standard: {std_colors}, Design-focused: {design_colors}")
        
        # Compare typography data
        std_font = standard_profile.get('typography', {}).get('primary_font', {}).get('family')
        design_font = design_profile.get('typography', {}).get('primary_font', {}).get('family')
        print(f"   Primary Font - Standard: {std_font or 'None'}, Design-focused: {design_font or 'None'}")
        
        # Compare layout data
        std_layout = bool(standard_profile.get('layout_system', {}))
        design_layout = bool(design_profile.get('layout_system', {}))
        print(f"   Layout System - Standard: {std_layout}, Design-focused: {design_layout}")

@pytest.mark.parametrize("brand_name", ["Apple", "Google", "Airbnb"])
def test_design_focused_research(brand_name):
    """Test design-focused research for brands with good design documentation."""
    success = run_design_focused_research_test(brand_name, force_refresh=True)
    assert success, f"Design-focused research failed for {brand_name}"

def test_apple_design_collection():
    """Specific test for Apple's design collection."""
    success = run_design_focused_research_test("Apple", force_refresh=True)
    assert success, "Apple design-focused research failed"

if __name__ == "__main__":
    # Test with brands known to have good design documentation
    test_brands = [
        "Apple",  # Known for comprehensive design guidelines
        "Google",  # Material Design system
        "Airbnb"   # Well-documented design system
    ]

    print("🧪 ENHANCED DESIGN COLLECTION TESTING")
    print("=" * 60)

    for brand in test_brands:
        success = run_design_focused_research_test(brand, force_refresh=True)
        if success:
            print(f"✅ {brand} test completed successfully")
        else:
            print(f"❌ {brand} test failed")
        print("\n" + "="*60 + "\n")

    # Optional: Run comparison test
    # compare_with_standard_research("Apple")
