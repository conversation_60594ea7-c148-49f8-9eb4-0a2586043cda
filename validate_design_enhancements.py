#!/usr/bin/env python3
"""
Simple validation script for enhanced design-focused brand collection.
"""

import json
import os
from app.brand_research import <PERSON><PERSON><PERSON><PERSON><PERSON>

def validate_design_enhancements():
    """Validate that the design enhancements are working."""
    print("🧪 VALIDATING DESIGN ENHANCEMENTS")
    print("=" * 50)
    
    try:
        # Test 1: Check if design-focused search queries are available
        print("1. Testing design-focused search queries...")
        from app.search_utils import get_design_focused_brand_queries
        design_queries = get_design_focused_brand_queries()
        print(f"   ✅ Found {len(design_queries)} design-focused search queries")
        
        # Test 2: Check if enhanced schema is available
        print("2. Testing enhanced brand schema...")
        from create_brand_agent import COMPREHENSIVE_BRAND_SCHEMA
        
        # Check for design-related fields
        color_system = COMPREHENSIVE_BRAND_SCHEMA.get('properties', {}).get('color_system', {})
        typography = COMPREHENSIVE_BRAND_SCHEMA.get('properties', {}).get('typography', {})
        layout_system = COMPREHENSIVE_BRAND_SCHEMA.get('properties', {}).get('layout_system', {})
        design_tokens = COMPREHENSIVE_BRAND_SCHEMA.get('properties', {}).get('design_tokens', {})
        
        if color_system and typography and layout_system and design_tokens:
            print("   ✅ Enhanced schema with design fields is available")
        else:
            print("   ❌ Enhanced schema is missing design fields")
            return False
        
        # Test 3: Check if design-focused prompts are available
        print("3. Testing design-focused analysis prompts...")
        from prompts.brand_creation import build_design_focused_analysis_prompt
        
        test_prompt = build_design_focused_analysis_prompt(
            "TestBrand", 
            ["Test content"], 
            COMPREHENSIVE_BRAND_SCHEMA
        )
        
        if "COLOR SYSTEM ANALYSIS" in test_prompt and "TYPOGRAPHY DEEP ANALYSIS" in test_prompt:
            print("   ✅ Design-focused analysis prompt is working")
        else:
            print("   ❌ Design-focused analysis prompt is missing key sections")
            return False
        
        # Test 4: Check if BrandResearcher has design-focused method
        print("4. Testing BrandResearcher design-focused method...")
        researcher = BrandResearcher()
        
        if hasattr(researcher, 'research_brand_with_design_focus'):
            print("   ✅ Design-focused research method is available")
        else:
            print("   ❌ Design-focused research method is missing")
            return False
        
        # Test 5: Check if prompt experiments are available
        print("5. Testing design-focused prompt experiments...")
        experiments_dir = "prompts/experiments"
        
        if os.path.exists(experiments_dir):
            experiment_files = [f for f in os.listdir(experiments_dir) if f.endswith('.json')]
            print(f"   ✅ Found {len(experiment_files)} prompt experiments")
        else:
            print("   ❌ Experiments directory not found")
            return False
        
        print("\n🎉 ALL DESIGN ENHANCEMENTS VALIDATED SUCCESSFULLY!")
        print("\nNext steps:")
        print("- Run a test with a real brand: docker-compose exec app python -c \"from app.brand_research import research_brand; research_brand('Apple', design_focused=True)\"")
        print("- Compare results with standard research")
        print("- Fine-tune prompts based on results")
        
        return True
        
    except Exception as e:
        print(f"❌ Validation failed with error: {e}")
        return False

def test_search_integration():
    """Test that search integration works with design focus."""
    print("\n🔍 TESTING SEARCH INTEGRATION")
    print("-" * 30)
    
    try:
        from app.search_utils import search_brand_urls
        
        # Test design-focused search
        print("Testing design-focused search for Apple...")
        design_urls = search_brand_urls("Apple", max_results=5, design_focused=True)
        
        if design_urls:
            print(f"   ✅ Found {len(design_urls)} design-focused URLs")
            print("   Sample URLs:")
            for i, url in enumerate(design_urls[:3]):
                print(f"     {i+1}. {url}")
        else:
            print("   ❌ No design-focused URLs found")
            return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ Search integration test failed: {e}")
        return False

if __name__ == "__main__":
    success = validate_design_enhancements()
    
    if success:
        test_search_integration()
        print("\n✅ VALIDATION COMPLETE - System ready for testing!")
    else:
        print("\n❌ VALIDATION FAILED - Please check the issues above")
