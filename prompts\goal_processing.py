"""
Goal Processing Prompts
Centralized prompts for processing user goals and creating image requirements.
"""

def build_goal_processing_prompt(goal_description: str, brand_profile, article_content=None) -> str:
    """Build the goal processing prompt with dynamic content."""
    
    # Extract locale information for language requirements
    locales_supported = brand_profile.get('metadata', {}).get('locales_supported', [])
    language_note = ""
    if locales_supported:
        primary_locale = locales_supported[0]
        if primary_locale.lower() in ['nederland', 'netherlands', 'nl', 'nl-nl']:
            language_note = " (Note: Content should be in Dutch/Nederlands)"
        elif primary_locale.lower() in ['belgië', 'belgium', 'be', 'nl-be']:
            language_note = " (Note: Content should be in Dutch/Nederlands)"
        elif primary_locale.lower() in ['france', 'fr', 'fr-fr']:
            language_note = " (Note: Content should be in French/Français)"
        elif primary_locale.lower() in ['germany', 'de', 'de-de']:
            language_note = " (Note: Content should be in German/Deutsch)"
        elif primary_locale.lower() in ['spain', 'es', 'es-es']:
            language_note = " (Note: Content should be in Spanish/Español)"
        elif primary_locale.lower() not in ['worldwide', 'global', 'en', 'en-us', 'en-gb']:
            language_note = f" (Note: Content should be in the local language for {primary_locale})"

    prompt_parts = [
        "You are an expert visual content strategist. Analyze the following goal and create specific image requirements.",
        "",
        f"GOAL: {goal_description}",
        "",
        "BRAND PROFILE:",
        f"Brand: {brand_profile.get('metadata', {}).get('brand_name', 'Brand')}",
        f"Description: {brand_profile.get('metadata', {}).get('description', '')}",
        f"Tone: {', '.join(brand_profile.get('metadata', {}).get('tone_of_voice', {}).get('keywords', [])) or 'Professional'}",
        f"Target Markets: {', '.join(locales_supported) if locales_supported else 'Global'}{language_note}",
        f"Primary Colors: {', '.join([c.get('name', '') for c in brand_profile.get('color_system', {}).get('primary', [])])}",
        f"Secondary Colors: {', '.join([c.get('name', '') for c in brand_profile.get('color_system', {}).get('secondary', [])])}",
        f"Typography: {brand_profile.get('typography', {}).get('primary_font', {}).get('family', 'Default')}",
    ]
    
    # Add article content information if available
    if article_content:
        prompt_parts.extend([
            "",
            "ARTICLE CONTENT CONTEXT:",
            f"Title: {article_content.title}",
            f"Summary: {article_content.summary}",
            f"Key Points: {', '.join(article_content.key_points)}",
            f"Main Themes: {', '.join(article_content.main_themes)}",
            f"Tone: {article_content.tone}",
            f"Target Audience: {article_content.target_audience}"
        ])
        if article_content.call_to_action:
            prompt_parts.append(f"Call to Action: {article_content.call_to_action}")
        
        # Add detailed section information
        if article_content.sections:
            prompt_parts.extend([
                "",
                "ARTICLE SECTIONS/TOPICS:",
                f"The article contains {len(article_content.sections)} distinct sections/topics:"
            ])
            for i, section in enumerate(article_content.sections, 1):
                prompt_parts.extend([
                    f"",
                    f"Section {i}: {section.title}",
                    f"  Type: {section.section_type}",
                    f"  Key Points: {', '.join(section.key_points[:3])}",
                    f"  Themes: {', '.join(section.themes[:3])}",
                    f"  Content Preview: {section.content[:150]}{'...' if len(section.content) > 150 else ''}"
                ])
    
    prompt_parts.extend([
        "",
        "Based on this information, determine:",
        "1. How many images should be created",
        "2. What each image should focus on",
        "3. How to structure the content for maximum impact",
        "4. What visual elements would best support the goal",
        "5. How to maintain brand consistency across all images"
    ])
    
    if article_content:
        prompt_parts.extend([
            "6. How to incorporate the article's key themes and messages into the visual design",
            "7. How to align the images with the article's tone and target audience",
            "8. IMPORTANT: Map each image to a specific article section/topic when possible",
            "9. Use section titles as image titles when appropriate",
            "10. For each image's key_points, include specific, detailed content from the relevant article section that should be visually represented or referenced in that image. Don't just use generic themes - use actual content details, statistics, quotes, or specific concepts from the article section."
        ])
        
        if article_content.sections:
            prompt_parts.extend([
                "",
                "SECTION MAPPING GUIDELINES:",
                "- When creating multiple images, try to map each image to a specific article section",
                "- Use the section title as the image title when it makes sense",
                "- Include section-specific key points and themes in each image's requirements",
                "- Ensure each image has a clear, specific focus based on its mapped section",
                "- If creating fewer images than sections, combine related sections or focus on the most important ones"
            ])
    
    return "\n".join(prompt_parts)

GOAL_PROCESSING_JSON_FORMAT = '''
Provide your response in this exact JSON format:

{
    "analysis": "string explaining your reasoning and approach",
    "content_format": "string describing the type of content (e.g., 'single_image', 'linkedin_carousel', 'instagram_series')",
    "images": [
        {
            "purpose": "string describing what this image is for",
            "title": "string to be used in the image as a title (use section title when applicable)",
            "content_focus": "string describing what the image should focus on",
            "design_notes": "string with additional design considerations",
            "key_points": ["specific", "detailed", "content", "from", "article", "section", "not", "generic", "themes"],
            "section_reference": "string - title of the article section this image represents (null if not section-specific)"
        }
    ],
    "overall_strategy": "string describing the overall visual strategy"
}

GUIDELINES FOR CREATING MEANINGFUL KEY_POINTS:
- Include specific details, statistics, quotes, or concepts from the article
- Avoid generic themes like "innovation" or "growth" - be specific
- Reference actual content that should be visually represented
- Use concrete examples from the article content
- Focus on actionable or memorable information

Example of GOOD key_points:
["Revenue increased 23% in Q3 due to new product launches", "Customer satisfaction scores improved from 7.2 to 8.9", "New AI-powered features reduced processing time by 40%"]

Example of BAD key_points:
["Growth", "Innovation", "Success", "Quality"]

Provide only the JSON response:'''

# Version tracking
GOAL_PROCESSING_VERSION = "v2.1"
GOAL_PROCESSING_CHANGELOG = """
v2.1: Added section mapping guidelines and enhanced key_points instructions
v2.0: Added article section support and section mapping
v1.0: Basic goal processing without article context
"""
