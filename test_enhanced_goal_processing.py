#!/usr/bin/env python3
"""
Quick test script to verify enhanced goal processing works correctly.
"""

import sys
from pathlib import Path

# Add the current directory to Python path for imports
sys.path.append(str(Path(__file__).parent))

from app.goal_processor import process_goal
from app.article_processor import process_content

def test_enhanced_goal_processing():
    """Test that goal processing works with and without article content."""
    
    print("🧪 Testing Enhanced Goal Processing")
    print("=" * 50)
    
    # Test content
    test_content = """
    Sustainable Innovation at Coca-Cola
    
    At Coca-Cola, we're committed to creating a more sustainable future through innovative packaging solutions. 
    Our new PlantBottle technology represents a breakthrough in sustainable packaging, using up to 30% plant-based materials.
    
    Key benefits:
    - Reduced carbon footprint
    - Renewable materials
    - Same quality and safety
    - Recyclable design
    
    Join us in our mission to refresh the world while protecting our planet for future generations.
    """
    
    test_goal = "LinkedIn Carousel about sustainability"
    
    try:
        # Get a dummy brand profile for testing
        brand_profile = {"brand_name": "Test Brand", "colors": ["#000000"], "style": "modern"}

        # Test 1: Basic goal processing (without article content)
        print("\n1️⃣ Testing basic goal processing...")
        goal_basic = process_goal(test_goal, brand_profile)
        print(f"✅ Basic result: {goal_basic.content_type}, {goal_basic.image_count} images")
        print(f"   Style: {goal_basic.overall_style[:80]}...")

        # Test 2: Process article content
        print("\n2️⃣ Processing article content...")
        article_content = process_content(test_content, "campaign")
        print(f"✅ Article processed: {article_content.title}")
        print(f"   Key points: {len(article_content.key_points)} points")
        print(f"   Tone: {article_content.tone}")

        # Test 3: Enhanced goal processing (with article content)
        print("\n3️⃣ Testing enhanced goal processing...")
        goal_enhanced = process_goal(test_goal, brand_profile, article_content)
        print(f"✅ Enhanced result: {goal_enhanced.content_type}, {goal_enhanced.image_count} images")
        print(f"   Style: {goal_enhanced.overall_style[:80]}...")
        
        # Test 4: Compare results
        print("\n📊 Comparison:")
        print(f"   Image count: {goal_basic.image_count} → {goal_enhanced.image_count}")
        print(f"   Style changed: {'Yes' if goal_basic.overall_style != goal_enhanced.overall_style else 'No'}")
        
        if goal_enhanced.images:
            print(f"   First image purpose: {goal_enhanced.images[0].purpose}")
            print(f"   First image focus: {goal_enhanced.images[0].content_focus}")
        
        print("\n🎉 Enhanced goal processing is working correctly!")
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_enhanced_goal_processing()
    sys.exit(0 if success else 1)
