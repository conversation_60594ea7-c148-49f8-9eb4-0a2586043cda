"""
Centralized Prompt Manager
Handles all prompt generation, versioning, and experimentation.
"""

import json
import os
from datetime import datetime
from typing import Dict, Any, Optional
from dataclasses import dataclass

from .article_analysis import ARTICLE_ANALYSIS_PROMPT, ARTICLE_ANALYSIS_VERSION
from .goal_processing import build_goal_processing_prompt, GOAL_PROCESSING_JSON_FORMAT, GOAL_PROCESSING_VERSION
from .image_generation import build_image_generation_prompt, IMAGE_GENERATION_VERSION
from .brand_creation import build_brand_analysis_prompt, build_brand_profile_prompt, build_design_focused_analysis_prompt, BRAND_CREATION_VERSION


@dataclass
class PromptExperiment:
    """Represents a prompt experiment for A/B testing."""
    name: str
    version: str
    description: str
    prompt_template: str
    created_at: str
    performance_notes: str = ""


class PromptManager:
    """Centralized manager for all prompts with experimentation support."""
    
    def __init__(self, experiments_dir: str = "prompts/experiments"):
        self.experiments_dir = experiments_dir
        self.ensure_experiments_dir()
        self.active_experiments = {}
        self.load_experiments()
    
    def ensure_experiments_dir(self):
        """Ensure experiments directory exists."""
        os.makedirs(self.experiments_dir, exist_ok=True)
    
    def get_article_analysis_prompt(self, content_type: str, content: str, experiment_name: str = None) -> str:
        """Get article analysis prompt, optionally using an experiment version."""
        if experiment_name and experiment_name in self.active_experiments:
            experiment = self.active_experiments[experiment_name]
            return experiment.prompt_template.format(content_type=content_type, content=content)
        
        return ARTICLE_ANALYSIS_PROMPT.format(content_type=content_type, content=content)
    
    def get_goal_processing_prompt(self, goal_description: str, brand_profile, article_content=None, experiment_name: str = None) -> str:
        """Get goal processing prompt, optionally using an experiment version."""
        if experiment_name and experiment_name in self.active_experiments:
            experiment = self.active_experiments[experiment_name]
            # For experiments, you'd need to implement custom formatting logic
            return experiment.prompt_template
        
        base_prompt = build_goal_processing_prompt(goal_description, brand_profile, article_content)
        return f"{base_prompt}\n\n{GOAL_PROCESSING_JSON_FORMAT}"
    
    def get_image_generation_prompt(self, brand_profile, image_req, article_content=None, experiment_name: str = None) -> str:
        """Get image generation prompt, optionally using an experiment version."""
        if experiment_name and experiment_name in self.active_experiments:
            experiment = self.active_experiments[experiment_name]
            # For experiments, you'd need to implement custom formatting logic
            return experiment.prompt_template
        
        return build_image_generation_prompt(brand_profile, image_req, article_content)
    
    def create_experiment(self, prompt_type: str, name: str, description: str, prompt_template: str) -> str:
        """Create a new prompt experiment."""
        experiment = PromptExperiment(
            name=name,
            version=f"{prompt_type}_experiment_{len(self.active_experiments) + 1}",
            description=description,
            prompt_template=prompt_template,
            created_at=datetime.now().isoformat()
        )
        
        # Save experiment to file
        experiment_file = os.path.join(self.experiments_dir, f"{name}.json")
        with open(experiment_file, 'w') as f:
            json.dump({
                'name': experiment.name,
                'version': experiment.version,
                'description': experiment.description,
                'prompt_template': experiment.prompt_template,
                'created_at': experiment.created_at,
                'performance_notes': experiment.performance_notes
            }, f, indent=2)
        
        self.active_experiments[name] = experiment
        return experiment.version
    
    def load_experiments(self):
        """Load all experiments from the experiments directory."""
        if not os.path.exists(self.experiments_dir):
            return
        
        for filename in os.listdir(self.experiments_dir):
            if filename.endswith('.json'):
                filepath = os.path.join(self.experiments_dir, filename)
                try:
                    with open(filepath, 'r') as f:
                        data = json.load(f)
                        experiment = PromptExperiment(**data)
                        self.active_experiments[experiment.name] = experiment
                except Exception as e:
                    print(f"Error loading experiment {filename}: {e}")
    
    def list_experiments(self) -> Dict[str, PromptExperiment]:
        """List all available experiments."""
        return self.active_experiments.copy()

    def get_brand_analysis_prompt(self, brand_name: str, text_content: list, schema: dict, experiment_name: str = None) -> str:
        """Get brand analysis prompt for vision-based brand research."""
        if experiment_name and experiment_name in self.active_experiments:
            experiment = self.active_experiments[experiment_name]
            # For experiments, you'd need to implement custom formatting logic
            return experiment.prompt_template.format(
                brand_name=brand_name,
                text_content=text_content,
                schema=schema
            )

        return build_brand_analysis_prompt(brand_name, text_content, schema)

    def get_brand_profile_prompt(self, brand_name: str, search_results: str, schema: dict, experiment_name: str = None) -> str:
        """Get brand profile creation prompt for text-based research."""
        if experiment_name and experiment_name in self.active_experiments:
            experiment = self.active_experiments[experiment_name]
            return experiment.prompt_template.format(
                brand_name=brand_name,
                search_results=search_results,
                schema=schema
            )

        return build_brand_profile_prompt(brand_name, search_results, schema)

    def get_simple_brand_prompt(self, brand_name: str, experiment_name: str = None) -> str:
        """Get simple brand creation prompt."""
        if experiment_name and experiment_name in self.active_experiments:
            experiment = self.active_experiments[experiment_name]
            return experiment.prompt_template.format(brand_name=brand_name)

        from .brand_creation import build_simple_brand_prompt
        return build_simple_brand_prompt(brand_name)

    def get_design_focused_analysis_prompt(self, brand_name: str, text_content: list, schema: dict, experiment_name: str = None) -> str:
        """Get design-focused brand analysis prompt for enhanced visual design extraction."""
        if experiment_name and experiment_name in self.active_experiments:
            experiment = self.active_experiments[experiment_name]
            return experiment.prompt_template.format(
                brand_name=brand_name,
                text_content=text_content,
                schema=schema
            )

        return build_design_focused_analysis_prompt(brand_name, text_content, schema)

    def get_prompt_versions(self) -> Dict[str, str]:
        """Get current versions of all prompt types."""
        return {
            'article_analysis': ARTICLE_ANALYSIS_VERSION,
            'goal_processing': GOAL_PROCESSING_VERSION,
            'image_generation': IMAGE_GENERATION_VERSION,
            'brand_creation': BRAND_CREATION_VERSION
        }
    
    def add_performance_note(self, experiment_name: str, note: str):
        """Add a performance note to an experiment."""
        if experiment_name in self.active_experiments:
            experiment = self.active_experiments[experiment_name]
            experiment.performance_notes += f"\n{datetime.now().isoformat()}: {note}"
            
            # Save updated experiment
            experiment_file = os.path.join(self.experiments_dir, f"{experiment_name}.json")
            with open(experiment_file, 'w') as f:
                json.dump({
                    'name': experiment.name,
                    'version': experiment.version,
                    'description': experiment.description,
                    'prompt_template': experiment.prompt_template,
                    'created_at': experiment.created_at,
                    'performance_notes': experiment.performance_notes
                }, f, indent=2)


# Global prompt manager instance
prompt_manager = PromptManager()
