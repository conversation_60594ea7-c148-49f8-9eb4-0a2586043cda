# Prompt Experiments

This directory contains experimental prompt variations for A/B testing and optimization.

## Structure

Each experiment is stored as a JSON file with the following structure:

```json
{
  "name": "experiment_name",
  "version": "v1.0",
  "description": "What this experiment tests",
  "prompt_template": "The actual prompt template",
  "created_at": "2024-01-01T00:00:00",
  "performance_notes": "Notes about performance and results"
}
```

## Usage

1. Create experiments using the PromptManager:
   ```python
   from prompts.prompt_manager import prompt_manager
   
   prompt_manager.create_experiment(
       prompt_type="article_analysis",
       name="shorter_sections",
       description="Test shorter, more focused section extraction",
       prompt_template="Your experimental prompt here..."
   )
   ```

2. Use experiments in your code:
   ```python
   prompt = prompt_manager.get_article_analysis_prompt(
       content_type="article",
       content="...",
       experiment_name="shorter_sections"
   )
   ```

3. Track performance:
   ```python
   prompt_manager.add_performance_note(
       "shorter_sections",
       "Generated better section titles, but missed some key points"
   )
   ```

## Experiment Ideas

### Article Analysis
- **shorter_sections**: Focus on 2-3 main sections only
- **detailed_extraction**: Extract more detailed key points per section
- **topic_clustering**: Group related topics into sections

### Goal Processing
- **concise_requirements**: Generate fewer, more focused image requirements
- **detailed_mapping**: More explicit section-to-image mapping
- **creative_titles**: Focus on more engaging image titles

### Image Generation
- **minimal_context**: Use less article context for cleaner prompts
- **visual_focus**: Emphasize visual elements over text content
- **brand_emphasis**: Stronger focus on brand consistency

### Brand Creation
- **enhanced_color_extraction**: Better color detection from screenshots
- **typography_focus**: Improved font identification and usage patterns
- **logo_variations**: Enhanced logo variation detection
- **asset_extraction**: Better brand asset URL identification
- **guideline_focus**: Extract actionable brand guidelines
- **quick_analysis**: Streamlined prompts for rapid brand research

## Best Practices

1. **One Variable**: Test one change at a time
2. **Clear Naming**: Use descriptive experiment names
3. **Document Results**: Add performance notes after testing
4. **Version Control**: Keep track of what works and what doesn't
5. **Baseline Comparison**: Always compare against the current production prompt
