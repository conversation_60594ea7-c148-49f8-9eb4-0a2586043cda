#!/usr/bin/env python3
"""
Brand Image Generator - New Architecture
Creates on-brand images based on flexible goals and article content.

This is the new unified entry point that implements the restructured architecture:
1. Goal-agnostic brand research
2. Flexible goal processing
3. Article-based content generation
4. Dynamic image generation

Usage Examples:
    # Research brand only
    python brand_image_generator.py research "Coca-Cola"
    
    # Generate images with inline content
    python brand_image_generator.py generate "Nike" \
        --goal "Instagram Story for new product launch" \
        --content "Introducing our new eco-friendly running shoes made from recycled materials..."
    
    # Generate images with article file
    python brand_image_generator.py generate "Apple" \
        --goal "LinkedIn Carousel about innovation" \
        --article "path/to/article.txt"
    
    # Force refresh brand research
    python brand_image_generator.py generate "Coca-Cola" \
        --goal "Product showcase banner" \
        --content "Our new sustainable packaging initiative..." \
        --refresh-brand
"""

import argparse
import sys
import os
from pathlib import Path

# Add the current directory to Python path for imports
sys.path.append(str(Path(__file__).parent))

from app.brand_research import research_brand
from app.goal_processor import process_goal
from app.article_processor import process_content, process_file
from app.image_generator import generate_images


def print_banner():
    """Print application banner."""
    print("=" * 70)
    print("🎨 BRAND IMAGE GENERATOR - New Architecture")
    print("=" * 70)
    print("Create on-brand images with flexible goals and article content")
    print()


def research_brand_command(brand_name: str, force_refresh: bool = False):
    """Execute brand research command."""
    print(f"🔍 Researching brand: {brand_name}")
    print(f"Force refresh: {force_refresh}")
    print("-" * 50)
    
    try:
        brand_profile = research_brand(brand_name, force_refresh=force_refresh)
        
        if brand_profile:
            print("✅ Brand research completed successfully!")
            print(f"📁 Brand profile saved for: {brand_name}")
            
            # Show basic brand info
            metadata = brand_profile.get('metadata', {})
            if metadata.get('description'):
                print(f"📝 Description: {metadata['description'][:100]}...")
            
            # Show available colors
            color_system = brand_profile.get('color_system', {})
            primary_colors = color_system.get('primary', [])
            if primary_colors:
                color_names = [c.get('name') for c in primary_colors if c.get('name')]
                if color_names:
                    print(f"🎨 Primary colors: {', '.join(color_names)}")
        else:
            print("❌ Brand research failed - no data could be gathered")
            
    except Exception as e:
        print(f"❌ Error during brand research: {e}")


def generate_images_command(
    brand_name: str, 
    goal: str, 
    content: str = None, 
    article_file: str = None,
    force_refresh_brand: bool = False
):
    """Execute image generation command."""
    print(f"🎨 Generating images for: {brand_name}")
    print(f"🎯 Goal: {goal}")
    print(f"🔄 Refresh brand: {force_refresh_brand}")
    print("-" * 50)
    
    try:
        # Step 1: Research/Load brand profile
        print("Step 1: Brand Research")
        brand_profile = research_brand(brand_name, force_refresh=force_refresh_brand)
        
        if not brand_profile:
            print("❌ Could not obtain brand profile. Cannot proceed.")
            return
        
        print(f"✅ Brand profile loaded for: {brand_name}")

        # Step 2: Process article/content first
        print("\nStep 2: Content Processing")
        if article_file:
            if not os.path.exists(article_file):
                print(f"❌ Article file not found: {article_file}")
                return
            article_content = process_file(article_file)
            print(f"✅ Article processed from file: {article_file}")
        elif content:
            article_content = process_content(content)
            print(f"✅ Content processed: {article_content.title}")
        else:
            print("❌ No content provided. Use --content or --article")
            return

        # Step 3: Process goal with article content
        print("\nStep 3: Goal Processing")
        goal_requirements = process_goal(goal, brand_profile, article_content)

        print(f"✅ Goal processed: {goal_requirements.content_type}")
        print(f"📊 Images to generate: {goal_requirements.image_count}")

        # Step 4: Generate images
        print("\nStep 4: Image Generation")
        result = generate_images(brand_profile, goal_requirements, article_content)
        
        if result.success:
            print(f"✅ Successfully generated {len(result.images)} images!")
            print("\nGenerated Images:")
            for i, img in enumerate(result.images, 1):
                print(f"  {i}. {img.purpose}")
                if img.local_path:
                    print(f"     📁 {img.local_path}")
            
            # Save generation summary
            summary_dir = "generation_summaries"
            os.makedirs(summary_dir, exist_ok=True)
            
            brand_id = brand_name.lower().replace(' ', '_')
            goal_id = goal.lower().replace(' ', '_')[:20]
            summary_file = f"{summary_dir}/{brand_id}_{goal_id}_summary.txt"
            
            with open(summary_file, 'w', encoding='utf-8') as f:
                f.write(result.generation_summary)
            
            print(f"\n📄 Generation summary saved: {summary_file}")
            
        else:
            print(f"❌ Image generation failed: {result.error_message}")
            
    except Exception as e:
        print(f"❌ Error during image generation: {e}")


def main():
    """Main function with enhanced CLI interface."""
    parser = argparse.ArgumentParser(
        description="Brand Image Generator - Create on-brand images with flexible goals",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s research "Coca-Cola"
      Research and cache brand profile
      
  %(prog)s generate "Nike" --goal "Instagram Story for product launch" --content "New eco-friendly shoes..."
      Generate images with inline content
      
  %(prog)s generate "Apple" --goal "LinkedIn Carousel about innovation" --article "article.txt"
      Generate images from article file
      
  %(prog)s generate "Coca-Cola" --goal "Hero banner" --content "Sustainability initiative..." --refresh-brand
      Generate images with fresh brand research
        """
    )
    
    # Subcommands
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Research command
    research_parser = subparsers.add_parser('research', help='Research and cache brand profile')
    research_parser.add_argument('brand_name', help='Name of the brand to research')
    research_parser.add_argument('--refresh', action='store_true', 
                                help='Force refresh even if profile exists')
    
    # Generate command
    generate_parser = subparsers.add_parser('generate', help='Generate on-brand images')
    generate_parser.add_argument('brand_name', help='Name of the brand')
    generate_parser.add_argument('--goal', required=True, 
                                help='Goal description (e.g., "LinkedIn Carousel about sustainability")')
    
    # Content input (mutually exclusive)
    content_group = generate_parser.add_mutually_exclusive_group(required=True)
    content_group.add_argument('--content', 
                              help='Inline content text')
    content_group.add_argument('--article', 
                              help='Path to article file')
    
    generate_parser.add_argument('--refresh-brand', action='store_true',
                                help='Force refresh brand research')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    print_banner()
    
    if args.command == 'research':
        research_brand_command(args.brand_name, args.refresh)
        
    elif args.command == 'generate':
        generate_images_command(
            args.brand_name, 
            args.goal, 
            args.content, 
            args.article,
            args.refresh_brand
        )


if __name__ == "__main__":
    main()
